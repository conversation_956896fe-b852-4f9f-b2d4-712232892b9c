@charset "UTF-8";
:root {
  --federal-blue: #1E293B;
  /* ciemny granat z nutą szaro<PERSON>ci, biznesowy akcent */
  --marian-blue: #334155;
  /* spokojny niebiesko-grafitowy */
  --honolulu-blue: #3B82A0;
  /* stonowany teal zamiast jaskrawego */
  --blue-green: #4A90A6;
  /* lekko przygaszony morski */
  --pacific-cyan: #5BB2B5;
  /* zgaszony cyan, mniej saturacji */
  --vivid-sky-blue: #7FC8D9;
  /* pastelowy błękit, używać oszczędnie */
  --non-photo-blue: #A7DCE8;
  /* jasny, neutralny błękit */
  --non-photo-blue-2: #D4EEF5;
  /* bardzo jasny, zgaszony */
  --light-cyan: #F3F8FA;
  /* prawie biel, przy<PERSON>zne tło */
  /* Neutral */
  --color-bg: #F9FAFB;
  --color-surface: #FFFFFF;
  --color-border: #E5E7EB;
  --color-text: #111827;
  --color-text-secondary: #6B7280;
  /* Accents */
  --color-primary: #2563EB;
  --color-primary-hover: #1D4ED8;
  --color-success: #16A34A;
  --color-warning: #F59E0B;
  --color-error: #DC2626;
  --font-family-base: "Inter", "Roboto", sans-serif;
  --font-size-h1: 24px;
  --font-size-h2: 18px;
  --font-size-h3: 16px;
  --font-size-body: 14px;
  --font-size-small: 12px;
  --line-height-base: 1.5;
}

* {
  transition: all 0.3s ease;
}

body {
  background: linear-gradient(135deg, var(--non-photo-blue-2) 0%, var(--light-cyan) 100%);
  min-height: 100vh;
}

.btn {
  border-radius: 25px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  border: none !important;
  transition: all 0.3s ease !important;
}
.btn:hover {
  animation: blink 0.6s ease-in-out;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(3, 4, 94, 0.3) !important;
}

.btn-primary {
  background: var(--color-primary);
  color: #fff;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: var(--font-size-body);
  font-weight: 600;
}

.btn-primary:hover {
  background: var(--color-primary-hover);
}

.btn-secondary {
  background: #fff;
  border: 1px solid var(--color-border);
  color: var(--color-text);
  border-radius: 4px;
  padding: 8px 16px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 50%, var(--honolulu-blue) 100%) !important;
  color: white !important;
}

.btn-secondary {
  background: linear-gradient(135deg, var(--blue-green) 0%, var(--pacific-cyan) 100%) !important;
  color: white !important;
}

.btn-success {
  background: linear-gradient(135deg, var(--honolulu-blue) 0%, var(--blue-green) 100%) !important;
  color: white !important;
}

.btn-info {
  background: linear-gradient(135deg, var(--pacific-cyan) 0%, var(--vivid-sky-blue) 100%) !important;
  color: white !important;
}

.btn-warning {
  background: linear-gradient(135deg, var(--vivid-sky-blue) 0%, var(--non-photo-blue) 100%) !important;
  color: var(--federal-blue) !important;
}

.btn-danger {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
  color: white !important;
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
.sidebar-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%);
}
.sidebar-header .logo {
  color: white;
  text-decoration: none;
  font-size: 1.25rem;
  font-weight: bold;
  display: flex;
  align-items: center;
}
.sidebar-header .logo i {
  margin-right: 0.5rem;
  color: var(--vivid-sky-blue);
}

.sidebar-menu {
  padding: 0;
  margin: 0;
  list-style: none;
  background: linear-gradient(180deg, var(--marian-blue) 0%, var(--honolulu-blue) 100%);
}
.sidebar-menu .menu-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.sidebar-menu .menu-item .menu-link {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all 0.3s ease;
}
.sidebar-menu .menu-item .menu-link:hover {
  background: linear-gradient(90deg, var(--pacific-cyan) 0%, var(--vivid-sky-blue) 100%);
  color: white;
  animation: blink 0.6s ease-in-out;
}
.sidebar-menu .menu-item .menu-link.active {
  background: linear-gradient(90deg, var(--blue-green) 0%, var(--pacific-cyan) 100%);
  color: white;
  border-right: 3px solid var(--vivid-sky-blue);
}
.sidebar-menu .menu-item .menu-link i {
  margin-right: 0.75rem;
  width: 20px;
  text-align: center;
  color: var(--non-photo-blue-2);
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  margin-top: auto;
  background: linear-gradient(135deg, var(--honolulu-blue) 0%, var(--blue-green) 100%);
}
.sidebar-footer small {
  color: var(--light-cyan);
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block !important;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
    color: white !important;
    border: none !important;
    padding: 0.75rem !important;
    border-radius: 6px !important;
    transition: all 0.3s ease !important;
  }
  .mobile-menu-toggle:hover {
    animation: blink 0.6s ease-in-out;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(3, 4, 94, 0.3) !important;
  }
}
@media (min-width: 769px) {
  .mobile-menu-toggle {
    display: none !important;
  }
}
.border-left-primary {
  border-left: 0.25rem solid var(--federal-blue) !important;
}

.border-left-success {
  border-left: 0.25rem solid var(--blue-green) !important;
}

.border-left-info {
  border-left: 0.25rem solid var(--pacific-cyan) !important;
}

.border-left-warning {
  border-left: 0.25rem solid var(--vivid-sky-blue) !important;
}

.text-gray-800 {
  color: var(--federal-blue) !important;
}

.text-gray-300 {
  color: var(--non-photo-blue) !important;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.text-xs {
  font-size: 0.7rem;
}

.shadow {
  box-shadow: 0 0.15rem 1.75rem 0 rgba(3, 4, 94, 0.15) !important;
}

.card {
  background: var(--color-surface);
  border-radius: 6px;
  border: 1px solid var(--color-border);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  padding: 16px;
}

.card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #E5E7EB;
  background: #fff;
  margin: -16px -16px 0 -16px;
  border-radius: 6px 6px 0;
}

.card__title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.card__actions {
  display: flex;
  gap: 8px;
}

.bg-primary {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
}

.bg-secondary {
  background: linear-gradient(135deg, var(--blue-green) 0%, var(--pacific-cyan) 100%) !important;
}

.bg-light {
  background: linear-gradient(135deg, var(--light-cyan) 0%, var(--non-photo-blue-2) 100%) !important;
}

.bg-dark {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
}

.form-control {
  border-radius: 6px !important;
  border: 2px solid var(--non-photo-blue-2) !important;
  transition: all 0.3s ease !important;
}
.form-control:focus {
  border-color: var(--pacific-cyan) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 180, 216, 0.25) !important;
  transform: scale(1.02) !important;
}

.form-select {
  border-radius: 6px !important;
  border: 2px solid var(--non-photo-blue-2) !important;
  transition: all 0.3s ease !important;
}
.form-select:focus {
  border-color: var(--pacific-cyan) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 180, 216, 0.25) !important;
}

.alert {
  border-radius: 6px !important;
  border: none !important;
}
.alert.alert-primary {
  background: linear-gradient(135deg, var(--vivid-sky-blue) 0%, var(--non-photo-blue) 100%) !important;
  color: var(--federal-blue) !important;
}
.alert.alert-success {
  background: linear-gradient(135deg, var(--blue-green) 0%, var(--pacific-cyan) 100%) !important;
  color: white !important;
}
.alert.alert-danger {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
  color: white !important;
}
.alert.alert-warning {
  background: linear-gradient(135deg, var(--vivid-sky-blue) 0%, var(--non-photo-blue) 100%) !important;
  color: var(--federal-blue) !important;
}
.alert.alert-info {
  background: linear-gradient(135deg, var(--pacific-cyan) 0%, var(--vivid-sky-blue) 100%) !important;
  color: white !important;
}

.dropdown-menu {
  border-radius: 6px !important;
  border: none !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(202, 240, 248, 0.3) 100%) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 10px 30px rgba(3, 4, 94, 0.15) !important;
}
.dropdown-menu .dropdown-item {
  border-radius: 10px !important;
  margin: 2px 5px !important;
  transition: all 0.3s ease !important;
}
.dropdown-menu .dropdown-item:hover {
  background: linear-gradient(135deg, var(--vivid-sky-blue) 0%, var(--non-photo-blue) 100%) !important;
  color: var(--federal-blue) !important;
  transform: translateX(5px) !important;
}
.dropdown-menu .dropdown-header {
  color: var(--federal-blue) !important;
  font-weight: 600 !important;
}

.breadcrumb {
  background: none !important;
  padding: 0 !important;
}
.breadcrumb .breadcrumb-item a {
  color: var(--honolulu-blue) !important;
  transition: all 0.3s ease !important;
}
.breadcrumb .breadcrumb-item a:hover {
  color: var(--federal-blue) !important;
  text-decoration: none !important;
}
.breadcrumb .breadcrumb-item.active {
  color: var(--marian-blue) !important;
}

.badge {
  border-radius: 6px !important;
}
.badge.bg-danger {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
}
.badge.bg-primary {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--honolulu-blue) 100%) !important;
}
.badge.bg-success {
  background: linear-gradient(135deg, var(--blue-green) 0%, var(--pacific-cyan) 100%) !important;
}

.table {
  border-radius: 6px !important;
  overflow: hidden !important;
}
.table thead th {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
  color: white !important;
  border: none !important;
  font-weight: 600 !important;
}
.table tbody tr {
  transition: all 0.3s ease !important;
}
.table tbody tr:hover {
  background: linear-gradient(135deg, var(--light-cyan) 0%, var(--non-photo-blue-2) 100%) !important;
  transform: scale(1.01) !important;
}
.table td,
.table th {
  border-color: var(--non-photo-blue-2) !important;
}

.progress {
  border-radius: 6px !important;
  background: var(--light-cyan) !important;
}
.progress .progress-bar {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--honolulu-blue) 100%) !important;
  border-radius: 6px !important;
}

.list-group .list-group-item {
  border-radius: 10px !important;
  margin-bottom: 5px !important;
  border: 2px solid var(--non-photo-blue-2) !important;
  transition: all 0.3s ease !important;
}
.list-group .list-group-item:hover {
  background: linear-gradient(135deg, var(--light-cyan) 0%, var(--non-photo-blue-2) 100%) !important;
  transform: translateX(5px) !important;
  border-color: var(--pacific-cyan) !important;
}
.list-group .list-group-item.active {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--honolulu-blue) 100%) !important;
  border-color: var(--federal-blue) !important;
}

.h-100 {
  height: 100% !important;
}

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*=col-] {
  padding-right: 0;
  padding-left: 0;
}

.chart-area,
.chart-pie {
  position: relative;
  height: 10rem;
  width: 100%;
}

@media (min-width: 768px) {
  .chart-area,
  .chart-pie {
    height: 20rem;
  }
}

/*# sourceMappingURL=app.css.map */
