// Bootstrap CSS is imported in the base template

// Color palette
:root {
  --federal-blue: #03045eff;
  --marian-blue: #023e8aff;
  --honolulu-blue: #0077b6ff;
  --blue-green: #0096c7ff;
  --pacific-cyan: #00b4d8ff;
  --vivid-sky-blue: #48cae4ff;
  --non-photo-blue: #90e0efff;
  --non-photo-blue-2: #ade8f4ff;
  --light-cyan: #caf0f8ff;
}

// Global styles
* {
  transition: all 0.3s ease;
}

// body {
//   background: linear-gradient(135deg, var(--light-cyan) 0%, var(--non-photo-blue-2) 50%, var(--vivid-sky-blue) 100%);
//   min-height: 100vh;
//   background-color: red !important;
// }

// Button styles
.btn {
  border-radius: 25px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  border: none !important;
  transition: all 0.3s ease !important;

  &:hover {
    animation: blink 0.6s ease-in-out;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(3, 4, 94, 0.3) !important;
  }
}

.btn-primary {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 50%, var(--honolulu-blue) 100%) !important;
  color: white !important;
}

.btn-secondary {
  background: linear-gradient(135deg, var(--blue-green) 0%, var(--pacific-cyan) 100%) !important;
  color: white !important;
}

.btn-success {
  background: linear-gradient(135deg, var(--honolulu-blue) 0%, var(--blue-green) 100%) !important;
  color: white !important;
}

.btn-info {
  background: linear-gradient(135deg, var(--pacific-cyan) 0%, var(--vivid-sky-blue) 100%) !important;
  color: white !important;
}

.btn-warning {
  background: linear-gradient(135deg, var(--vivid-sky-blue) 0%, var(--non-photo-blue) 100%) !important;
  color: var(--federal-blue) !important;
}

.btn-danger {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
  color: white !important;
}

@keyframes blink {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

// Custom styles for sidebar menu
.sidebar-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%);

  .logo {
    color: white;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: bold;
    display: flex;
    align-items: center;

    i {
      margin-right: 0.5rem;
      color: var(--vivid-sky-blue);
    }
  }
}

.sidebar-menu {
  padding: 0;
  margin: 0;
  list-style: none;
  background: linear-gradient(180deg, var(--marian-blue) 0%, var(--honolulu-blue) 100%);

  .menu-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .menu-link {
      display: flex;
      align-items: center;
      padding: 1rem 1.5rem;
      color: rgba(255, 255, 255, 0.9);
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(90deg, var(--pacific-cyan) 0%, var(--vivid-sky-blue) 100%);
        color: white;
        animation: blink 0.6s ease-in-out;
      }

      &.active {
        background: linear-gradient(90deg, var(--blue-green) 0%, var(--pacific-cyan) 100%);
        color: white;
        border-right: 3px solid var(--vivid-sky-blue);
      }

      i {
        margin-right: 0.75rem;
        width: 20px;
        text-align: center;
        color: var(--non-photo-blue-2);
      }
    }
  }
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  margin-top: auto;
  background: linear-gradient(135deg, var(--honolulu-blue) 0%, var(--blue-green) 100%);

  small {
    color: var(--light-cyan);
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block !important;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
    color: white !important;
    border: none !important;
    padding: 0.75rem !important;
    border-radius: 15px !important;
    transition: all 0.3s ease !important;

    &:hover {
      animation: blink 0.6s ease-in-out;
      transform: translateY(-2px) !important;
      box-shadow: 0 5px 15px rgba(3, 4, 94, 0.3) !important;
    }
  }
}

@media (min-width: 769px) {
  .mobile-menu-toggle {
    display: none !important;
  }
}

// Dashboard specific styles
.border-left-primary {
  border-left: 0.25rem solid var(--federal-blue) !important;
}

.border-left-success {
  border-left: 0.25rem solid var(--blue-green) !important;
}

.border-left-info {
  border-left: 0.25rem solid var(--pacific-cyan) !important;
}

.border-left-warning {
  border-left: 0.25rem solid var(--vivid-sky-blue) !important;
}

.text-gray-800 {
  color: var(--federal-blue) !important;
}

.text-gray-300 {
  color: var(--non-photo-blue) !important;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.text-xs {
  font-size: 0.7rem;
}

.shadow {
  box-shadow: 0 0.15rem 1.75rem 0 rgba(3, 4, 94, 0.15) !important;
}

// Card styles
.card {
  border-radius: 15px !important;
  border: none !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(202, 240, 248, 0.3) 100%) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 8px 32px rgba(3, 4, 94, 0.1) !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 40px rgba(3, 4, 94, 0.2) !important;
  }
}

.card-header {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
  color: white !important;
  border-radius: 15px 15px 0 0 !important;
  border-bottom: none !important;
}

// Background classes
.bg-primary {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
}

.bg-secondary {
  background: linear-gradient(135deg, var(--blue-green) 0%, var(--pacific-cyan) 100%) !important;
}

.bg-light {
  background: linear-gradient(135deg, var(--light-cyan) 0%, var(--non-photo-blue-2) 100%) !important;
}

.bg-dark {
  background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
}

// Form styles
.form-control {
  border-radius: 15px !important;
  border: 2px solid var(--non-photo-blue-2) !important;
  transition: all 0.3s ease !important;

  &:focus {
    border-color: var(--pacific-cyan) !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 180, 216, 0.25) !important;
    transform: scale(1.02) !important;
  }
}

.form-select {
  border-radius: 15px !important;
  border: 2px solid var(--non-photo-blue-2) !important;
  transition: all 0.3s ease !important;

  &:focus {
    border-color: var(--pacific-cyan) !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 180, 216, 0.25) !important;
  }
}

// Alert styles
.alert {
  border-radius: 15px !important;
  border: none !important;

  &.alert-primary {
    background: linear-gradient(135deg, var(--vivid-sky-blue) 0%, var(--non-photo-blue) 100%) !important;
    color: var(--federal-blue) !important;
  }

  &.alert-success {
    background: linear-gradient(135deg, var(--blue-green) 0%, var(--pacific-cyan) 100%) !important;
    color: white !important;
  }

  &.alert-danger {
    background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
    color: white !important;
  }

  &.alert-warning {
    background: linear-gradient(135deg, var(--vivid-sky-blue) 0%, var(--non-photo-blue) 100%) !important;
    color: var(--federal-blue) !important;
  }

  &.alert-info {
    background: linear-gradient(135deg, var(--pacific-cyan) 0%, var(--vivid-sky-blue) 100%) !important;
    color: white !important;
  }
}

// Dropdown styles
.dropdown-menu {
  border-radius: 15px !important;
  border: none !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(202, 240, 248, 0.3) 100%) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 10px 30px rgba(3, 4, 94, 0.15) !important;

  .dropdown-item {
    border-radius: 10px !important;
    margin: 2px 5px !important;
    transition: all 0.3s ease !important;

    &:hover {
      background: linear-gradient(135deg, var(--vivid-sky-blue) 0%, var(--non-photo-blue) 100%) !important;
      color: var(--federal-blue) !important;
      transform: translateX(5px) !important;
    }
  }

  .dropdown-header {
    color: var(--federal-blue) !important;
    font-weight: 600 !important;
  }
}

// Breadcrumb styles
.breadcrumb {
  background: none !important;
  padding: 0 !important;

  .breadcrumb-item {
    a {
      color: var(--honolulu-blue) !important;
      transition: all 0.3s ease !important;

      &:hover {
        color: var(--federal-blue) !important;
        text-decoration: none !important;
      }
    }

    &.active {
      color: var(--marian-blue) !important;
    }
  }
}

// Badge styles
.badge {
  border-radius: 15px !important;

  &.bg-danger {
    background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
  }

  &.bg-primary {
    background: linear-gradient(135deg, var(--federal-blue) 0%, var(--honolulu-blue) 100%) !important;
  }

  &.bg-success {
    background: linear-gradient(135deg, var(--blue-green) 0%, var(--pacific-cyan) 100%) !important;
  }
}

// Table styles
.table {
  border-radius: 15px !important;
  overflow: hidden !important;

  thead th {
    background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 100%) !important;
    color: white !important;
    border: none !important;
    font-weight: 600 !important;
  }

  tbody tr {
    transition: all 0.3s ease !important;

    &:hover {
      background: linear-gradient(135deg, var(--light-cyan) 0%, var(--non-photo-blue-2) 100%) !important;
      transform: scale(1.01) !important;
    }
  }

  td,
  th {
    border-color: var(--non-photo-blue-2) !important;
  }
}

// Progress bar styles
.progress {
  border-radius: 15px !important;
  background: var(--light-cyan) !important;

  .progress-bar {
    background: linear-gradient(135deg, var(--federal-blue) 0%, var(--honolulu-blue) 100%) !important;
    border-radius: 15px !important;
  }
}

// List group styles
.list-group {
  .list-group-item {
    border-radius: 10px !important;
    margin-bottom: 5px !important;
    border: 2px solid var(--non-photo-blue-2) !important;
    transition: all 0.3s ease !important;

    &:hover {
      background: linear-gradient(135deg, var(--light-cyan) 0%, var(--non-photo-blue-2) 100%) !important;
      transform: translateX(5px) !important;
      border-color: var(--pacific-cyan) !important;
    }

    &.active {
      background: linear-gradient(135deg, var(--federal-blue) 0%, var(--honolulu-blue) 100%) !important;
      border-color: var(--federal-blue) !important;
    }
  }
}

.h-100 {
  height: 100% !important;
}

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.no-gutters>.col,
.no-gutters>[class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}

.chart-area,
.chart-pie {
  position: relative;
  height: 10rem;
  width: 100%;
}

@media (min-width: 768px) {

  .chart-area,
  .chart-pie {
    height: 20rem;
  }
}