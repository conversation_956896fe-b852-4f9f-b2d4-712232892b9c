import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static values = { 
        url: String,
        type: String 
    }

    async test() {
        const button = this.element
        const originalText = button.innerHTML
        
        // Show loading state
        button.disabled = true
        button.innerHTML = '<i class="ti ti-loader-2 animate-spin"></i> Testing...'
        
        try {
            const formData = new FormData()
            formData.append('type', this.typeValue)
            
            const response = await fetch(this.urlValue, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            
            const data = await response.json()
            
            if (data.success) {
                this.showSuccess(data.message)
                button.classList.remove('btn-outline-primary', 'btn-outline-info')
                button.classList.add('btn-success')
            } else {
                this.showError(data.message)
                button.classList.remove('btn-outline-primary', 'btn-outline-info')
                button.classList.add('btn-danger')
            }
            
        } catch (error) {
            this.showError('Connection test failed: ' + error.message)
            button.classList.remove('btn-outline-primary', 'btn-outline-info')
            button.classList.add('btn-danger')
        } finally {
            button.disabled = false
            button.innerHTML = originalText
            
            // Reset button color after 3 seconds
            setTimeout(() => {
                button.classList.remove('btn-success', 'btn-danger')
                if (this.typeValue === 'smtp') {
                    button.classList.add('btn-outline-primary')
                } else {
                    button.classList.add('btn-outline-info')
                }
            }, 3000)
        }
    }
    
    showSuccess(message) {
        this.showToast(message, 'success')
    }
    
    showError(message) {
        this.showToast(message, 'error')
    }
    
    showToast(message, type) {
        // Create toast notification
        const toast = document.createElement('div')
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;'
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `
        
        document.body.appendChild(toast)
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast)
            }
        }, 5000)
    }
}
