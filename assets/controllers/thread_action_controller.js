import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static values = { url: String }

    async execute() {
        const button = this.element
        const originalText = button.innerHTML
        
        // Show loading state
        button.disabled = true
        button.innerHTML = '<i class="ti ti-loader-2 animate-spin"></i> Processing...'
        
        try {
            const formData = new FormData()
            
            const response = await fetch(this.urlValue, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            
            if (response.ok) {
                // Redirect to the response URL or reload page
                window.location.reload()
            } else {
                throw new Error('Action failed')
            }
            
        } catch (error) {
            this.showError('Action failed: ' + error.message)
            button.disabled = false
            button.innerHTML = originalText
        }
    }
    
    showError(message) {
        const toast = document.createElement('div')
        toast.className = 'alert alert-danger alert-dismissible fade show position-fixed'
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;'
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `
        
        document.body.appendChild(toast)
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast)
            }
        }, 5000)
    }
}
