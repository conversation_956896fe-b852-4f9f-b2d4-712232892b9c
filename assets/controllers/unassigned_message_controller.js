import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static values = { messageId: String }

    connect() {
        // Add event listeners for dropdown actions
        this.element.addEventListener('click', (event) => {
            const action = event.target.dataset.action
            const messageId = event.target.dataset.messageId
            
            if (action === 'create-thread') {
                this.showCreateThreadModal(messageId)
            } else if (action === 'assign-thread') {
                this.showAssignThreadModal(messageId)
            }
        })
    }

    showCreateThreadModal(messageId) {
        // Create modal for thread creation
        const modal = this.createModal('Create New Thread', `
            <form data-controller="create-thread-form" data-message-id="${messageId}">
                <div class="mb-3">
                    <label class="form-label">Customer Email</label>
                    <input type="email" class="form-control" name="customer_email" required>
                    <div class="form-text">Enter customer email to search or create new customer</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Order ID (optional)</label>
                    <input type="text" class="form-control" name="order_id">
                    <div class="form-text">Link this thread to a specific order</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Thread Subject</label>
                    <input type="text" class="form-control" name="subject" required>
                </div>
                <div class="d-flex justify-content-end">
                    <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Thread</button>
                </div>
            </form>
        `)
        
        document.body.appendChild(modal)
        const bsModal = new bootstrap.Modal(modal)
        bsModal.show()
        
        // Clean up when modal is hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal)
        })
    }

    showAssignThreadModal(messageId) {
        // Create modal for thread assignment
        const modal = this.createModal('Assign to Existing Thread', `
            <form data-controller="assign-thread-form" data-message-id="${messageId}">
                <div class="mb-3">
                    <label class="form-label">Search Threads</label>
                    <input type="text" class="form-control" name="thread_search" placeholder="Search by customer email or subject">
                    <div class="form-text">Start typing to search existing threads</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Select Thread</label>
                    <select class="form-select" name="thread_id" required>
                        <option value="">Choose a thread...</option>
                    </select>
                </div>
                <div class="d-flex justify-content-end">
                    <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign to Thread</button>
                </div>
            </form>
        `)
        
        document.body.appendChild(modal)
        const bsModal = new bootstrap.Modal(modal)
        bsModal.show()
        
        // Clean up when modal is hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal)
        })
    }

    createModal(title, content) {
        const modal = document.createElement('div')
        modal.className = 'modal fade'
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        `
        return modal
    }

    showSuccess(message) {
        const toast = document.createElement('div')
        toast.className = 'alert alert-success alert-dismissible fade show position-fixed'
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;'
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `
        
        document.body.appendChild(toast)
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast)
            }
        }, 5000)
    }

    showError(message) {
        const toast = document.createElement('div')
        toast.className = 'alert alert-danger alert-dismissible fade show position-fixed'
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;'
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `
        
        document.body.appendChild(toast)
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast)
            }
        }, 5000)
    }
}
