<?php

namespace App\Module\Custom\Order;

use App\Engine\Hook\RegionComponent;
use App\Module\AbstractModule;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class OrderModule extends AbstractModule {

    public function __construct(
        private readonly UrlGeneratorInterface $urlGenerator,
    ) {
    }

    public function getId(): string
    {
        return 'orderModule';
    }

    public function getName(): string
    {
        return 'Order Module';
    }

    public function getDescription(): string
    {
        return 'Order module to help us with orders';
    }

    public function getVersion(): string
    {
        return '1.0.0';
    }

    public function getDependencies(): array
    {
        return [
            // 'carrierModule'
        ];
    }

    public function getHooks(): array
    {
        return ['menu'];
    }

    public function hook_menu($menuItems) {
        $menuItems[] = [
            'label' => 'Orders',
            'url' => '#',
            'icon' => 'fas fa-puzzle-piece',
            'children' => [
                [
                    'label' => 'Orders List',
                    'route' => 'admin_order_list',
                    'icon' => 'fas fa-puzzle-piece',
                    'url' => $this->urlGenerator->generate('admin_order_list'),
                ],
                [
                    'label' => 'Order status tabs',
                    'route' => 'admin_order_status_tab_index',
                    'icon' => 'fas fa-puzzle-piece',
                    'url' => $this->urlGenerator->generate('admin_order_status_tab_index'),
                ],
                [
                    'label' => 'Order status list',
                    'route' => 'admin_order_status_index',
                    'icon' => 'fas fa-puzzle-piece',
                    'url' => $this->urlGenerator->generate('admin_order_status_index'),
                ]
            ],
        ];

        return $menuItems;
    }

    protected function hook_content_after(array $context): array
    {
        $request = $context['request'] ?? null;

        if (!$request) {
            return [];
        }

        return [
            RegionComponent::create('@example/footer_widget.html.twig', [
                'message' => 'Hello from Example Module!',
                'current_route' => $request->attributes->get('_route'),
                'timestamp' => new \DateTime()
            ], 10)
        ];
    }

    public function getConfigurationSchema(): array
    {
        return [
            'show_message' => [
                'type' => 'checkbox',
                'label' => 'Show Footer Message',
                'default' => true,
                'description' => 'Display example message in page footer'
            ],
            'message_text' => [
                'type' => 'text',
                'label' => 'Custom Message',
                'default' => 'Hello from Example Module!',
                'description' => 'Custom message to display'
            ],
            'message_color' => [
                'type' => 'select',
                'label' => 'Message Color',
                'options' => [
                    'primary' => 'Blue',
                    'success' => 'Green',
                    'warning' => 'Yellow',
                    'danger' => 'Red',
                    'info' => 'Light Blue'
                ],
                'default' => 'primary'
            ]
        ];
    }

    protected function onInstall(): void
    {

    }

    protected function onEnable(): void
    {

    }

    protected function onDisable(): void
    {

    }

    protected function onUninstall(): void
    {

    }
}