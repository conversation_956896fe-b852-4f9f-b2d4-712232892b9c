
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card__header bg-primary text-white">
                    <h3 class="card-title">
                        <i class="fas fa-puzzle-piece"></i> {{ title }}
                    </h3>
                </div>

                <div class="card-body">
                    <div class="alert alert-success">
                        <h4 class="alert-heading">
                            <i class="fas fa-check-circle"></i> Module System Working!
                        </h4>
                        <p>{{ message }}</p>
                        <hr>
                        <p class="mb-0">
                            This page is rendered by a controller from the Example module,
                            demonstrating the custom module system functionality.
                        </p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card__header">
                                    <h5>Module Features</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        {% for feature in features %}
                                            <li class="list-group-item">
                                                <i class="fas fa-check text-success"></i> {{ feature }}
                                            </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card__header">
                                    <h5>Module Structure</h5>
                                </div>
                                <div class="card-body">
                                    <pre class="bg-light p-3 rounded">
                                        <code>
src/Module/Example/
├── ExampleModule.php
├── Controller/
│   └── ExampleController.php
├── Service/
├── templates/
│   ├── index.html.twig
│   ├── config.html.twig
│   └── footer_widget.html.twig
└── ...</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>Test Module API</h5>
                        <button id="testApiBtn" class="btn btn-outline-primary">
                            <i class="fas fa-play"></i> Test API Endpoint
                        </button>
                        <div id="apiResult" class="mt-3"></div>
                    </div>

                    <div class="mt-4">
                        <a href="{{ path('example_config') }}" class="btn btn-primary">
                            <i class="fas fa-cog"></i> Module Configuration
                        </a>
                        <a href="{{ path('admin_modules') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Module Management
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('testApiBtn').addEventListener('click', function() {
        fetch('{{ path('example_api_status') }}')
            .then(response => response.json())
            .then(data => {
                document.getElementById('apiResult').innerHTML = `
                    <div class="alert alert-info">
                        <h6>API Response:</h6>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            })
            .catch(error => {
                document.getElementById('apiResult').innerHTML = `
                    <div class="alert alert-danger">
                        <h6>Error:</h6>
                        <p>${error.message}</p>
                    </div>
                `;
            });
    });
});
</script>
