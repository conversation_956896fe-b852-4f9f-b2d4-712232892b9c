<?php

namespace App\Module\Custom\UserService\ValueObject;

readonly class IncomingMessage
{
    public function __construct(
        public string $id,
        public string $fromEmail,
        public ?string $fromName,
        public string $subject,
        public string $body,
        public string $htmlBody,
        public \DateTimeInterface $receivedAt,
        public array $headers,
        public ?string $inReplyTo = null,
        public ?string $references = null,
        public ?string $messageId = null,
        public array $attachments = []
    ) {}

    public function getThreadIdentifier(): ?string
    {
        return $this->inReplyTo ?? $this->references;
    }

    public function isReply(): bool
    {
        return !empty($this->inReplyTo) || !empty($this->references);
    }

    public function getPlainTextBody(): string
    {
        if (!empty($this->body)) {
            return $this->body;
        }
        
        return strip_tags($this->htmlBody);
    }

    public function hasAttachments(): bool
    {
        return !empty($this->attachments);
    }

    public function getAttachmentCount(): int
    {
        return count($this->attachments);
    }
}
