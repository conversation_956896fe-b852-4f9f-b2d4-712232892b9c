<?php

namespace App\Module\Custom\UserService\ValueObject;

readonly class OutgoingMessage
{
    public function __construct(
        public string $toEmail,
        public ?string $toName,
        public string $subject,
        public string $body,
        public ?string $htmlBody = null,
        public ?string $inReplyTo = null,
        public ?string $references = null,
        public array $attachments = []
    ) {}

    public function isReply(): bool
    {
        return !empty($this->inReplyTo);
    }

    public function hasHtmlBody(): bool
    {
        return !empty($this->htmlBody);
    }

    public function hasAttachments(): bool
    {
        return !empty($this->attachments);
    }

    public function getAttachmentCount(): int
    {
        return count($this->attachments);
    }
}
