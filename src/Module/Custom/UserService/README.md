# UserService Module

Moduł UserService zapewnia kompleksowe zarządzanie komunikacją z klientami poprzez email, automatyczne tworzenie kont klientów oraz zarządzanie wątkami konwersacji.

## Funkcjonalności

### 1. Automatyczne tworzenie kont klientów
- Automatyczne tworzenie kont `Customer` na podstawie adresów email z zamówień (`Order`)
- Sprawdzanie czy klient już istnieje przed utworzeniem nowego
- Aktualizacja danych klienta z informacji z zamówienia

### 2. Zarządzanie kontami email
- Dodawanie, edycja i usuwanie kont email w panelu admina
- Konfiguracja SMTP i IMAP dla każdego konta
- Testowanie połączeń SMTP i IMAP
- Włączanie/wyłączanie pobierania wiadomości IMAP

### 3. Pobieranie wiadomości email
- Automatyczne pobieranie nowych wiadomości z serwerów IMAP
- Parsowanie nagłówków email do identyfikacji wątków
- Zapisywanie wiadomości w bazie danych
- Komenda konsolowa do pobierania wiadomości

### 4. Zarządzanie wątkami komunikacji
- Automatyczne tworzenie wątków na podstawie nagłówków email
- Przypisywanie wiadomości do istniejących wątków
- Łączenie wątków z konkretnymi zamówieniami
- Zamykanie wątków tymczasowo lub na stałe
- Panel do ręcznego zarządzania wątkami

### 5. Panel administracyjny
- Dashboard z podsumowaniem aktywności
- Lista kont email z możliwością zarządzania
- Widok wątków z funkcjami zarządzania
- Lista klientów z historią zamówień i wątków
- Interfejs do przypisywania nieprzetworzonych wiadomości

## Architektura

### Encje
- `Customer` - klienci systemu
- `EmailThread` - wątki komunikacji
- `EmailMessage` - pojedyncze wiadomości email
- Rozszerzenie `EmailAccount` o pola IMAP
- Rozszerzenie `Order` o relację do `Customer`

### Serwisy
- `CustomerService` - zarządzanie klientami
- `EmailAccountManagementService` - zarządzanie kontami email
- `EmailImapService` - pobieranie wiadomości IMAP
- `ThreadManagementService` - zarządzanie wątkami
- `EmailProcessingService` - przetwarzanie wiadomości

### Interfejsy
- `MessageChannelInterface` - abstrakcja kanałów komunikacji
- `MessageProviderInterface` - dostawcy wiadomości
- `MessageSenderInterface` - wysyłanie wiadomości

### Kontrolery
- `UserServiceController` - główny kontroler z dashboard i kontami email
- `ThreadController` - zarządzanie wątkami
- `CustomerController` - zarządzanie klientami

### Kontrolery Stimulus
- `email_account_form_controller.js` - formularz kont email
- `connection_test_controller.js` - testowanie połączeń
- `delete_confirm_controller.js` - potwierdzanie usuwania
- `thread_action_controller.js` - akcje na wątkach
- `unassigned_message_controller.js` - zarządzanie nieprzypisan

## Instalacja

1. Uruchom migrację bazy danych:
```bash
php bin/console doctrine:migrations:migrate
```

2. Skonfiguruj konta email w panelu admina

3. Włącz pobieranie wiadomości dla wybranych kont

4. Skonfiguruj cron job do automatycznego pobierania:
```bash
# Pobieranie co 5 minut
*/5 * * * * php /path/to/project/bin/console user-service:fetch-emails --auto-assign
```

## Użytkowanie

### Dodawanie konta email
1. Przejdź do "User Service" > "Email Accounts"
2. Kliknij "Add Email Account"
3. Wypełnij dane SMTP i opcjonalnie IMAP
4. Przetestuj połączenia
5. Zapisz konto

### Zarządzanie wątkami
1. Przejdź do "User Service" > "Threads"
2. Zobacz otwarte wątki i nieprzypisane wiadomości
3. Kliknij na wątek aby zobaczyć szczegóły
4. Przypisz wiadomości do wątków lub utwórz nowe

### Automatyczne przetwarzanie
- Wiadomości są automatycznie pobierane z kont IMAP
- System próbuje automatycznie przypisać je do wątków
- Nieprzypisane wiadomości wymagają ręcznej interwencji

## Rozszerzalność

Moduł został zaprojektowany z myślą o przyszłych integracjach:
- Interfejsy umożliwiają dodanie innych kanałów komunikacji (WhatsApp, SMS, etc.)
- Fabryki pozwalają na łatwe dodawanie nowych dostawców wiadomości
- Architektura DDD ułatwia rozszerzanie funkcjonalności

## Testy

Uruchom testy jednostkowe:
```bash
php bin/phpunit tests/Module/Custom/UserService/
```

## Bezpieczeństwo

- Hasła do kont email są przechowywane w bazie danych (zalecane szyfrowanie)
- Wszystkie akcje są logowane
- Walidacja danych wejściowych
- Ochrona przed duplikacją wiadomości
