<?php

namespace App\Module\Custom\UserService\Command;

use App\Module\Custom\UserService\Service\EmailProcessingService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'user-service:fetch-emails',
    description: 'Fetch new emails from all enabled IMAP accounts'
)]
class FetchEmailsCommand extends Command
{
    public function __construct(
        private readonly EmailProcessingService $emailProcessingService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('auto-assign', 'a', InputOption::VALUE_NONE, 'Auto-assign messages to threads after fetching')
            ->setHelp('This command fetches new emails from all enabled IMAP accounts and processes them.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Fetching emails from IMAP accounts');

        try {
            $results = $this->emailProcessingService->processAllEnabledAccounts();

            $totalProcessed = 0;
            $successfulAccounts = 0;
            $failedAccounts = 0;

            foreach ($results as $accountId => $result) {
                if ($result['success']) {
                    $successfulAccounts++;
                    $totalProcessed += $result['processed_count'];
                    $io->success("Account {$accountId}: {$result['processed_count']} messages processed");
                } else {
                    $failedAccounts++;
                    $io->error("Account {$accountId}: {$result['error']}");
                }
            }

            $io->section('Summary');
            $io->text([
                "Total accounts processed: " . count($results),
                "Successful: {$successfulAccounts}",
                "Failed: {$failedAccounts}",
                "Total messages processed: {$totalProcessed}"
            ]);

            if ($input->getOption('auto-assign')) {
                $io->section('Auto-assigning messages to threads');
                $assignedCount = $this->emailProcessingService->autoAssignMessagesToThreads();
                $io->success("Auto-assigned {$assignedCount} messages to threads");
            }

            $unprocessedCount = $this->emailProcessingService->getUnprocessedMessagesCount();
            if ($unprocessedCount > 0) {
                $io->warning("There are {$unprocessedCount} unassigned messages that require manual attention");
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error('Failed to fetch emails: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
