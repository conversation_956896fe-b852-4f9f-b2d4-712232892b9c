<?php

namespace App\Module\Custom\UserService\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class UserServiceExtension extends AbstractExtension
{
    public function getFilters(): array
    {
        return [
            new TwigFilter('md5', [$this, 'md5Filter']),
            new TwigFilter('gravatar', [$this, 'gravatarFilter']),
            new TwigFilter('date_diff', [$this, 'dateDiffFilter']),
        ];
    }

    public function md5Filter(string $string): string
    {
        return md5($string);
    }

    public function gravatarFilter(string $email, string $default = 'identicon', int $size = 40): string
    {
        $hash = md5(strtolower(trim($email)));
        return "https://www.gravatar.com/avatar/{$hash}?d={$default}&s={$size}";
    }

    public function dateDiffFilter(\DateTimeInterface $date): string
    {
        $now = new \DateTime();
        $diff = $now->diff($date);

        if ($diff->days > 0) {
            return $diff->days . ' days ago';
        } elseif ($diff->h > 0) {
            return $diff->h . ' hours ago';
        } elseif ($diff->i > 0) {
            return $diff->i . ' minutes ago';
        } else {
            return 'just now';
        }
    }
}
