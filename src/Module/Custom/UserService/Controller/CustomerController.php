<?php

namespace App\Module\Custom\UserService\Controller;

use App\Engine\Response\PageResponse;
use App\Module\Custom\UserService\Entity\Customer;
use App\Module\Custom\UserService\Service\CustomerService;
use App\Module\Custom\UserService\Service\ThreadManagementService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/user-service/customers')]
class CustomerController extends AbstractController
{
    public function __construct(
        private readonly CustomerService $customerService,
        private readonly ThreadManagementService $threadService
    ) {}

    #[Route('/', name: 'user_service_customers')]
    public function index(): PageResponse
    {
        $customers = $this->customerService->getActiveCustomers();
        
        return PageResponse::create('@userServiceModule/customers/index.html.twig', [
            'title' => 'Customers',
            'customers' => $customers
        ]);
    }

    #[Route('/{id}', name: 'user_service_customer_view')]
    public function view(Customer $customer): PageResponse
    {
        $orders = $this->customerService->getCustomerOrders($customer);
        $threads = $this->threadService->getThreadsForCustomer($customer);
        
        return PageResponse::create('@userServiceModule/customers/view.html.twig', [
            'title' => 'Customer: ' . $customer->getFullName(),
            'customer' => $customer,
            'orders' => $orders,
            'threads' => $threads
        ]);
    }

    #[Route('/search', name: 'user_service_customers_search', methods: ['GET'])]
    public function search(Request $request): JsonResponse
    {
        $searchTerm = $request->query->get('q', '');
        
        if (strlen($searchTerm) < 2) {
            return new JsonResponse(['customers' => []]);
        }
        
        $customers = $this->customerService->searchCustomers($searchTerm);
        
        $result = array_map(function (Customer $customer) {
            return [
                'id' => $customer->getId()->toRfc4122(),
                'email' => $customer->getEmail(),
                'full_name' => $customer->getFullName(),
                'phone' => $customer->getPhone()
            ];
        }, $customers);
        
        return new JsonResponse(['customers' => $result]);
    }

    #[Route('/{id}/deactivate', name: 'user_service_customer_deactivate', methods: ['POST'])]
    public function deactivate(Customer $customer): JsonResponse
    {
        try {
            $this->customerService->deactivateCustomer($customer);
            
            return new JsonResponse([
                'success' => true,
                'message' => 'Customer deactivated successfully'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error deactivating customer: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/activate', name: 'user_service_customer_activate', methods: ['POST'])]
    public function activate(Customer $customer): JsonResponse
    {
        try {
            $this->customerService->activateCustomer($customer);
            
            return new JsonResponse([
                'success' => true,
                'message' => 'Customer activated successfully'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error activating customer: ' . $e->getMessage()
            ]);
        }
    }
}
