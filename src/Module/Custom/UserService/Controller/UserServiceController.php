<?php

namespace App\Module\Custom\UserService\Controller;

use App\Core\Entity\EmailAccount;
use App\Engine\Response\PageResponse;
use App\Module\Custom\UserService\Service\EmailAccountManagementService;
use App\Module\Custom\UserService\Service\ThreadManagementService;
use App\Module\Custom\UserService\Service\CustomerService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Attribute\Route;
use Doctrine\ORM\EntityManagerInterface;

#[Route('/admin/user-service')]
class UserServiceController extends AbstractController {

    public function __construct(
        private readonly EmailAccountManagementService $emailAccountService,
        private readonly ThreadManagementService $threadService,
        private readonly CustomerService $customerService,
        private readonly EntityManagerInterface $entityManager
    ) {}

    #[Route('/', name: 'user_service_dashboard')]
    public function dashboard(): PageResponse {
        $openThreads = $this->threadService->getOpenThreads();
        $unassignedMessages = $this->threadService->getUnassignedMessages();
        $emailAccounts = $this->emailAccountService->getAllEmailAccounts();

        return PageResponse::create('@userServiceModule/dashboard.html.twig', [
            'title' => 'User Service Dashboard',
            'open_threads_count' => count($openThreads),
            'unassigned_messages_count' => count($unassignedMessages),
            'email_accounts_count' => count($emailAccounts)
        ]);
    }

    #[Route('/email-accounts', name: 'user_service_email_accounts')]
    public function emailAccounts(): PageResponse {
        $emailAccounts = $this->emailAccountService->getAllEmailAccounts();

        return PageResponse::create('@userServiceModule/email_accounts/index.html.twig', [
            'title' => 'Email Accounts',
            'email_accounts' => $emailAccounts
        ]);
    }

    #[Route('/email-accounts/new', name: 'user_service_email_accounts_new', methods: ['GET', 'POST'])]
    public function newEmailAccount(Request $request): PageResponse|RedirectResponse {
        if ($request->isMethod('POST')) {
            try {
                $data = $request->request->all();
                $this->emailAccountService->createEmailAccount($data);
                $this->addFlash('success', 'Email account created successfully');
                return $this->redirectToRoute('user_service_email_accounts');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error creating email account: ' . $e->getMessage());
            }
        }

        return PageResponse::create('@userServiceModule/email_accounts/form.html.twig', [
            'title' => 'New Email Account',
            'account' => null
        ]);
    }

    #[Route('/email-accounts/{id}/edit', name: 'user_service_email_accounts_edit', methods: ['GET', 'POST'])]
    public function editEmailAccount(EmailAccount $emailAccount, Request $request): PageResponse|RedirectResponse {
        if ($request->isMethod('POST')) {
            try {
                $data = $request->request->all();
                $this->emailAccountService->updateEmailAccount($emailAccount, $data);
                $this->addFlash('success', 'Email account updated successfully');
                return $this->redirectToRoute('user_service_email_accounts');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error updating email account: ' . $e->getMessage());
            }
        }

        return PageResponse::create('@userServiceModule/email_accounts/form.html.twig', [
            'title' => 'Edit Email Account',
            'account' => $emailAccount
        ]);
    }

    #[Route('/email-accounts/{id}/delete', name: 'user_service_email_accounts_delete', methods: ['POST'])]
    public function deleteEmailAccount(EmailAccount $emailAccount): RedirectResponse {
        try {
            $this->emailAccountService->deleteEmailAccount($emailAccount);
            $this->addFlash('success', 'Email account deleted successfully');
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error deleting email account: ' . $e->getMessage());
        }

        return $this->redirectToRoute('user_service_email_accounts');
    }

    #[Route('/email-accounts/{id}/test-connection', name: 'user_service_email_accounts_test', methods: ['POST'])]
    public function testEmailAccountConnection(EmailAccount $emailAccount, Request $request): JsonResponse {
        $type = $request->request->get('type', 'smtp');

        try {
            if ($type === 'imap') {
                $result = $this->emailAccountService->testImapConnection($emailAccount);
            } else {
                $result = $this->emailAccountService->testSmtpConnection($emailAccount);
            }

            return new JsonResponse([
                'success' => $result,
                'message' => $result ? 'Connection successful' : 'Connection failed'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Connection error: ' . $e->getMessage()
            ]);
        }
    }
}