<?php

namespace App\Module\Custom\UserService\Controller;

use App\Engine\Response\PageResponse;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/user-service')]
class UserServiceController extends AbstractController {

    public function __construct() {}

    public function listThreads(): PageResponse {
        return PageResponse::create('@userServiceModule/list_threads.html.twig', [
            'title' => 'User Service'
        ]);
    }
}