<?php

namespace App\Module\Custom\UserService\Controller;

use App\Core\Entity\Customer;
use App\Core\Entity\EmailMessage;
use App\Core\Entity\EmailThread;
use App\Core\Entity\Order;
use App\Engine\Response\PageResponse;
use App\Module\Custom\UserService\Service\CustomerService;
use App\Module\Custom\UserService\Service\ThreadManagementService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/user-service/threads')]
class ThreadController extends AbstractController
{
    public function __construct(
        private readonly ThreadManagementService $threadService,
        private readonly CustomerService $customerService,
        private readonly EntityManagerInterface $entityManager
    ) {}

    #[Route('/', name: 'user_service_threads')]
    public function index(): PageResponse
    {
        $openThreads = $this->threadService->getOpenThreads();
        $unassignedMessages = $this->threadService->getUnassignedMessages();
        
        return PageResponse::create('@userServiceModule/threads/index.html.twig', [
            'title' => 'Email Threads',
            'open_threads' => $openThreads,
            'unassigned_messages' => $unassignedMessages
        ]);
    }

    #[Route('/{id}', name: 'user_service_thread_view')]
    public function view(EmailThread $thread): PageResponse
    {
        $messages = $this->threadService->getThreadMessages($thread);
        $customerOrders = $this->customerService->getCustomerOrders($thread->getCustomer());
        
        return PageResponse::create('@userServiceModule/threads/view.html.twig', [
            'title' => 'Thread: ' . $thread->getSubject(),
            'thread' => $thread,
            'messages' => $messages,
            'customer_orders' => $customerOrders
        ]);
    }

    #[Route('/{id}/close-temporarily', name: 'user_service_thread_close_temporarily', methods: ['POST'])]
    public function closeTemporarily(EmailThread $thread): RedirectResponse
    {
        try {
            $this->threadService->closeThreadTemporarily($thread);
            $this->addFlash('success', 'Thread closed temporarily');
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error closing thread: ' . $e->getMessage());
        }
        
        return $this->redirectToRoute('user_service_thread_view', ['id' => $thread->getId()]);
    }

    #[Route('/{id}/close-permanently', name: 'user_service_thread_close_permanently', methods: ['POST'])]
    public function closePermanently(EmailThread $thread): RedirectResponse
    {
        try {
            $this->threadService->closeThreadPermanently($thread);
            $this->addFlash('success', 'Thread closed permanently');
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error closing thread: ' . $e->getMessage());
        }
        
        return $this->redirectToRoute('user_service_threads');
    }

    #[Route('/{id}/reopen', name: 'user_service_thread_reopen', methods: ['POST'])]
    public function reopen(EmailThread $thread): RedirectResponse
    {
        try {
            $this->threadService->reopenThread($thread);
            $this->addFlash('success', 'Thread reopened');
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error reopening thread: ' . $e->getMessage());
        }
        
        return $this->redirectToRoute('user_service_thread_view', ['id' => $thread->getId()]);
    }

    #[Route('/{id}/assign-order', name: 'user_service_thread_assign_order', methods: ['POST'])]
    public function assignOrder(EmailThread $thread, Request $request): JsonResponse
    {
        try {
            $orderId = $request->request->get('order_id');
            $order = $this->entityManager->getRepository(Order::class)->find($orderId);
            
            if (!$order) {
                return new JsonResponse(['success' => false, 'message' => 'Order not found']);
            }
            
            $this->threadService->updateThreadOrder($thread, $order);
            
            return new JsonResponse([
                'success' => true,
                'message' => 'Order assigned to thread successfully'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error assigning order: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/create-from-message/{id}', name: 'user_service_thread_create_from_message', methods: ['POST'])]
    public function createFromMessage(EmailMessage $message, Request $request): JsonResponse
    {
        try {
            $customerId = $request->request->get('customer_id');
            $orderId = $request->request->get('order_id');
            
            $customer = $this->entityManager->getRepository(Customer::class)->find($customerId);
            if (!$customer) {
                return new JsonResponse(['success' => false, 'message' => 'Customer not found']);
            }
            
            $order = null;
            if ($orderId) {
                $order = $this->entityManager->getRepository(Order::class)->find($orderId);
            }
            
            $thread = $this->threadService->createThreadFromMessage($message, $customer, $order);
            
            return new JsonResponse([
                'success' => true,
                'message' => 'Thread created successfully',
                'thread_id' => $thread->getId()->toRfc4122()
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error creating thread: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/assign-message/{messageId}/to-thread/{threadId}', name: 'user_service_assign_message_to_thread', methods: ['POST'])]
    public function assignMessageToThread(string $messageId, string $threadId): JsonResponse
    {
        try {
            $message = $this->entityManager->getRepository(EmailMessage::class)->find($messageId);
            $thread = $this->entityManager->getRepository(EmailThread::class)->find($threadId);
            
            if (!$message || !$thread) {
                return new JsonResponse(['success' => false, 'message' => 'Message or thread not found']);
            }
            
            $this->threadService->assignMessageToThread($message, $thread);
            
            return new JsonResponse([
                'success' => true,
                'message' => 'Message assigned to thread successfully'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error assigning message: ' . $e->getMessage()
            ]);
        }
    }
}
