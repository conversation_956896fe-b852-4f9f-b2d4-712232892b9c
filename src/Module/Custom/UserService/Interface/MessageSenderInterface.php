<?php

namespace App\Module\Custom\UserService\Interface;

use App\Module\Custom\UserService\ValueObject\OutgoingMessage;

interface MessageSenderInterface
{
    public function getChannelType(): string;
    
    public function sendMessage(OutgoingMessage $message, MessageChannelInterface $channel): bool;
    
    public function sendReply(OutgoingMessage $message, string $threadId, MessageChannelInterface $channel): bool;
    
    public function validateMessage(OutgoingMessage $message): bool;
    
    public function testConnection(MessageChannelInterface $channel): bool;
}
