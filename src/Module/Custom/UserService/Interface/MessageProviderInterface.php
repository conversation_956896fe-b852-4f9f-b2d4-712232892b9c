<?php

namespace App\Module\Custom\UserService\Interface;

use App\Module\Custom\UserService\ValueObject\IncomingMessage;

interface MessageProviderInterface
{
    public function getChannelType(): string;
    
    public function fetchNewMessages(MessageChannelInterface $channel): array;
    
    public function markMessageAsRead(string $messageId, MessageChannelInterface $channel): bool;
    
    public function parseMessage(array $rawMessage): IncomingMessage;
    
    public function extractThreadIdentifier(IncomingMessage $message): ?string;
    
    public function isReplyToExistingThread(IncomingMessage $message): bool;
    
    public function testConnection(MessageChannelInterface $channel): bool;
}
