<?php

namespace App\Module\Custom\UserService\Interface;

use App\Module\Custom\UserService\Entity\Customer;

interface MessageChannelInterface
{
    public function getId(): string;
    
    public function getName(): string;
    
    public function getType(): string;
    
    public function isActive(): bool;
    
    public function canReceiveMessages(): bool;
    
    public function canSendMessages(): bool;
    
    public function getConfiguration(): array;
    
    public function validateConfiguration(): bool;
}
