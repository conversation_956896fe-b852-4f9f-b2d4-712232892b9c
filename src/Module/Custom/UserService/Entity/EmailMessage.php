<?php

namespace App\Module\Custom\UserService\Entity;

use App\Core\Entity\EmailAccount;
use App\Module\Custom\UserService\Repository\EmailMessageRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: EmailMessageRepository::class)]
#[ORM\Table(name: 'user_service_email_messages')]
class EmailMessage
{
    public const DIRECTION_INCOMING = 'incoming';
    public const DIRECTION_OUTGOING = 'outgoing';

    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255, unique: true)]
    private string $externalMessageId;

    #[ORM\Column(length: 20)]
    private string $direction;

    #[ORM\Column(length: 255)]
    private string $fromEmail;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $fromName = null;

    #[ORM\Column(length: 255)]
    private string $toEmail;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $toName = null;

    #[ORM\Column(length: 500)]
    private string $subject;

    #[ORM\Column(type: Types::TEXT)]
    private string $body;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $htmlBody = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $headers = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $attachments = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private \DateTimeInterface $createdAt;

    #[ORM\Column(type: Types::BOOLEAN)]
    private bool $isRead = false;

    #[ORM\ManyToOne(targetEntity: EmailThread::class, inversedBy: 'messages')]
    #[ORM\JoinColumn(nullable: true)]
    private ?EmailThread $thread = null;

    #[ORM\ManyToOne(targetEntity: EmailAccount::class)]
    #[ORM\JoinColumn(nullable: false)]
    private EmailAccount $emailAccount;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $inReplyTo = null;

    #[ORM\Column(length: 500, nullable: true)]
    private ?string $references = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getExternalMessageId(): string
    {
        return $this->externalMessageId;
    }

    public function setExternalMessageId(string $externalMessageId): static
    {
        $this->externalMessageId = $externalMessageId;
        return $this;
    }

    public function getDirection(): string
    {
        return $this->direction;
    }

    public function setDirection(string $direction): static
    {
        $this->direction = $direction;
        return $this;
    }

    public function isIncoming(): bool
    {
        return $this->direction === self::DIRECTION_INCOMING;
    }

    public function isOutgoing(): bool
    {
        return $this->direction === self::DIRECTION_OUTGOING;
    }

    public function getFromEmail(): string
    {
        return $this->fromEmail;
    }

    public function setFromEmail(string $fromEmail): static
    {
        $this->fromEmail = $fromEmail;
        return $this;
    }

    public function getFromName(): ?string
    {
        return $this->fromName;
    }

    public function setFromName(?string $fromName): static
    {
        $this->fromName = $fromName;
        return $this;
    }

    public function getToEmail(): string
    {
        return $this->toEmail;
    }

    public function setToEmail(string $toEmail): static
    {
        $this->toEmail = $toEmail;
        return $this;
    }

    public function getToName(): ?string
    {
        return $this->toName;
    }

    public function setToName(?string $toName): static
    {
        $this->toName = $toName;
        return $this;
    }

    public function getSubject(): string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): static
    {
        $this->subject = $subject;
        return $this;
    }

    public function getBody(): string
    {
        return $this->body;
    }

    public function setBody(string $body): static
    {
        $this->body = $body;
        return $this;
    }

    public function getHtmlBody(): ?string
    {
        return $this->htmlBody;
    }

    public function setHtmlBody(?string $htmlBody): static
    {
        $this->htmlBody = $htmlBody;
        return $this;
    }

    public function getHeaders(): ?array
    {
        return $this->headers;
    }

    public function setHeaders(?array $headers): static
    {
        $this->headers = $headers;
        return $this;
    }

    public function getAttachments(): ?array
    {
        return $this->attachments;
    }

    public function setAttachments(?array $attachments): static
    {
        $this->attachments = $attachments;
        return $this;
    }

    public function hasAttachments(): bool
    {
        return !empty($this->attachments);
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function isRead(): bool
    {
        return $this->isRead;
    }

    public function setIsRead(bool $isRead): static
    {
        $this->isRead = $isRead;
        return $this;
    }

    public function markAsRead(): static
    {
        $this->isRead = true;
        return $this;
    }

    public function markAsUnread(): static
    {
        $this->isRead = false;
        return $this;
    }

    public function getThread(): ?EmailThread
    {
        return $this->thread;
    }

    public function setThread(?EmailThread $thread): static
    {
        $this->thread = $thread;
        return $this;
    }

    public function getEmailAccount(): EmailAccount
    {
        return $this->emailAccount;
    }

    public function setEmailAccount(EmailAccount $emailAccount): static
    {
        $this->emailAccount = $emailAccount;
        return $this;
    }

    public function getInReplyTo(): ?string
    {
        return $this->inReplyTo;
    }

    public function setInReplyTo(?string $inReplyTo): static
    {
        $this->inReplyTo = $inReplyTo;
        return $this;
    }

    public function getReferences(): ?string
    {
        return $this->references;
    }

    public function setReferences(?string $references): static
    {
        $this->references = $references;
        return $this;
    }

    public function isReply(): bool
    {
        return !empty($this->inReplyTo) || !empty($this->references);
    }

    public function getThreadIdentifier(): ?string
    {
        return $this->inReplyTo ?? $this->references;
    }
}
