<?php

namespace App\Module\Custom\UserService\Entity;

use App\Core\Entity\Order;
use App\Module\Custom\UserService\Repository\EmailThreadRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: EmailThreadRepository::class)]
#[ORM\Table(name: 'user_service_email_threads')]
class EmailThread
{
    public const STATUS_OPEN = 'open';
    public const STATUS_TEMPORARILY_CLOSED = 'temporarily_closed';
    public const STATUS_PERMANENTLY_CLOSED = 'permanently_closed';

    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255)]
    private string $subject;

    #[ORM\Column(length: 50)]
    private string $status = self::STATUS_OPEN;

    #[ORM\ManyToOne(targetEntity: Customer::class)]
    #[ORM\JoinColumn(nullable: false)]
    private Customer $customer;

    #[ORM\ManyToOne(targetEntity: Order::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?Order $relatedOrder = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private \DateTimeInterface $createdAt;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private \DateTimeInterface $updatedAt;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $lastMessageAt = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $originalMessageId = null;

    /**
     * @var Collection<int, EmailMessage>
     */
    #[ORM\OneToMany(targetEntity: EmailMessage::class, mappedBy: 'thread', cascade: ['persist', 'remove'])]
    #[ORM\OrderBy(['createdAt' => 'ASC'])]
    private Collection $messages;

    public function __construct()
    {
        $this->messages = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getSubject(): string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): static
    {
        $this->subject = $subject;
        $this->updateTimestamp();
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;
        $this->updateTimestamp();
        return $this;
    }

    public function isOpen(): bool
    {
        return $this->status === self::STATUS_OPEN;
    }

    public function isTemporarilyClosed(): bool
    {
        return $this->status === self::STATUS_TEMPORARILY_CLOSED;
    }

    public function isPermanentlyClosed(): bool
    {
        return $this->status === self::STATUS_PERMANENTLY_CLOSED;
    }

    public function open(): static
    {
        $this->status = self::STATUS_OPEN;
        $this->updateTimestamp();
        return $this;
    }

    public function closeTemporarily(): static
    {
        $this->status = self::STATUS_TEMPORARILY_CLOSED;
        $this->updateTimestamp();
        return $this;
    }

    public function closePermanently(): static
    {
        $this->status = self::STATUS_PERMANENTLY_CLOSED;
        $this->updateTimestamp();
        return $this;
    }

    public function getCustomer(): Customer
    {
        return $this->customer;
    }

    public function setCustomer(Customer $customer): static
    {
        $this->customer = $customer;
        return $this;
    }

    public function getRelatedOrder(): ?Order
    {
        return $this->relatedOrder;
    }

    public function setRelatedOrder(?Order $relatedOrder): static
    {
        $this->relatedOrder = $relatedOrder;
        $this->updateTimestamp();
        return $this;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): \DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function getLastMessageAt(): ?\DateTimeInterface
    {
        return $this->lastMessageAt;
    }

    public function setLastMessageAt(?\DateTimeInterface $lastMessageAt): static
    {
        $this->lastMessageAt = $lastMessageAt;
        return $this;
    }

    public function getOriginalMessageId(): ?string
    {
        return $this->originalMessageId;
    }

    public function setOriginalMessageId(?string $originalMessageId): static
    {
        $this->originalMessageId = $originalMessageId;
        return $this;
    }

    /**
     * @return Collection<int, EmailMessage>
     */
    public function getMessages(): Collection
    {
        return $this->messages;
    }

    public function addMessage(EmailMessage $message): static
    {
        if (!$this->messages->contains($message)) {
            $this->messages->add($message);
            $message->setThread($this);
            $this->lastMessageAt = $message->getCreatedAt();
            $this->updateTimestamp();
        }
        return $this;
    }

    public function removeMessage(EmailMessage $message): static
    {
        if ($this->messages->removeElement($message)) {
            if ($message->getThread() === $this) {
                $message->setThread(null);
            }
        }
        return $this;
    }

    public function getMessageCount(): int
    {
        return $this->messages->count();
    }

    public function getLastMessage(): ?EmailMessage
    {
        if ($this->messages->isEmpty()) {
            return null;
        }
        
        return $this->messages->last();
    }

    private function updateTimestamp(): void
    {
        $this->updatedAt = new \DateTime();
    }

    #[ORM\PreUpdate]
    public function preUpdate(): void
    {
        $this->updateTimestamp();
    }
}
