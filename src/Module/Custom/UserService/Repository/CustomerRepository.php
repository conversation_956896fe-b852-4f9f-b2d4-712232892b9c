<?php

namespace App\Module\Custom\UserService\Repository;

use App\Module\Custom\UserService\Entity\Customer;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Customer>
 */
class CustomerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Customer::class);
    }

    public function findByEmail(string $email): ?Customer
    {
        return $this->findOneBy(['email' => $email]);
    }

    public function findActiveCustomers(): array
    {
        return $this->createQueryBuilder('c')
            ->where('c.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('c.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findCustomersWithOrders(): array
    {
        return $this->createQueryBuilder('c')
            ->leftJoin('c.orders', 'o')
            ->where('o.id IS NOT NULL')
            ->andWhere('c.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('c.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function searchByEmailOrName(string $searchTerm): array
    {
        return $this->createQueryBuilder('c')
            ->where('c.email LIKE :search')
            ->orWhere('c.firstName LIKE :search')
            ->orWhere('c.lastName LIKE :search')
            ->andWhere('c.isActive = :active')
            ->setParameter('search', '%' . $searchTerm . '%')
            ->setParameter('active', true)
            ->orderBy('c.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function save(Customer $customer): void
    {
        $this->getEntityManager()->persist($customer);
        $this->getEntityManager()->flush();
    }

    public function remove(Customer $customer): void
    {
        $this->getEntityManager()->remove($customer);
        $this->getEntityManager()->flush();
    }
}
