<?php

namespace App\Module\Custom\UserService\Repository;

use App\Module\Custom\UserService\Entity\Customer;
use App\Module\Custom\UserService\Entity\EmailThread;
use App\Core\Entity\Order;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EmailThread>
 */
class EmailThreadRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmailThread::class);
    }

    public function findByCustomer(Customer $customer): array
    {
        return $this->createQueryBuilder('t')
            ->where('t.customer = :customer')
            ->setParameter('customer', $customer)
            ->orderBy('t.lastMessageAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findByOrder(Order $order): array
    {
        return $this->createQueryBuilder('t')
            ->where('t.relatedOrder = :order')
            ->setParameter('order', $order)
            ->orderBy('t.lastMessageAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findOpenThreads(): array
    {
        return $this->createQueryBuilder('t')
            ->where('t.status = :status')
            ->setParameter('status', EmailThread::STATUS_OPEN)
            ->orderBy('t.lastMessageAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findByOriginalMessageId(string $messageId): ?EmailThread
    {
        return $this->findOneBy(['originalMessageId' => $messageId]);
    }

    public function findActiveThreadsForCustomer(Customer $customer): array
    {
        return $this->createQueryBuilder('t')
            ->where('t.customer = :customer')
            ->andWhere('t.status IN (:statuses)')
            ->setParameter('customer', $customer)
            ->setParameter('statuses', [EmailThread::STATUS_OPEN, EmailThread::STATUS_TEMPORARILY_CLOSED])
            ->orderBy('t.lastMessageAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function save(EmailThread $thread): void
    {
        $this->getEntityManager()->persist($thread);
        $this->getEntityManager()->flush();
    }

    public function remove(EmailThread $thread): void
    {
        $this->getEntityManager()->remove($thread);
        $this->getEntityManager()->flush();
    }
}
