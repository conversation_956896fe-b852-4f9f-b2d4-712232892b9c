<?php

namespace App\Module\Custom\UserService\Repository;

use App\Module\Custom\UserService\Entity\EmailMessage;
use App\Module\Custom\UserService\Entity\EmailThread;
use App\Core\Entity\EmailAccount;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EmailMessage>
 */
class EmailMessageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmailMessage::class);
    }

    public function findByExternalMessageId(string $externalMessageId): ?EmailMessage
    {
        return $this->findOneBy(['externalMessageId' => $externalMessageId]);
    }

    public function findByThread(EmailThread $thread): array
    {
        return $this->createQueryBuilder('m')
            ->where('m.thread = :thread')
            ->setParameter('thread', $thread)
            ->orderBy('m.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findUnreadMessages(): array
    {
        return $this->createQueryBuilder('m')
            ->where('m.isRead = :isRead')
            ->andWhere('m.direction = :direction')
            ->setParameter('isRead', false)
            ->setParameter('direction', EmailMessage::DIRECTION_INCOMING)
            ->orderBy('m.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findByEmailAccount(EmailAccount $emailAccount): array
    {
        return $this->createQueryBuilder('m')
            ->where('m.emailAccount = :emailAccount')
            ->setParameter('emailAccount', $emailAccount)
            ->orderBy('m.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findUnassignedMessages(): array
    {
        return $this->createQueryBuilder('m')
            ->where('m.thread IS NULL')
            ->andWhere('m.direction = :direction')
            ->setParameter('direction', EmailMessage::DIRECTION_INCOMING)
            ->orderBy('m.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findByThreadIdentifier(string $threadIdentifier): array
    {
        return $this->createQueryBuilder('m')
            ->where('m.inReplyTo = :identifier')
            ->orWhere('m.references LIKE :identifierLike')
            ->setParameter('identifier', $threadIdentifier)
            ->setParameter('identifierLike', '%' . $threadIdentifier . '%')
            ->orderBy('m.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function save(EmailMessage $message): void
    {
        $this->getEntityManager()->persist($message);
        $this->getEntityManager()->flush();
    }

    public function remove(EmailMessage $message): void
    {
        $this->getEntityManager()->remove($message);
        $this->getEntityManager()->flush();
    }
}
