<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-print-none">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            <h2 class="page-title">{{ title }}</h2>
                            <div class="text-muted mt-1">Manage customer communication threads</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Open Threads</h3>
                </div>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Subject</th>
                                <th>Order</th>
                                <th>Last Message</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for thread in open_threads %}
                                <tr>
                                    <td>
                                        <div class="d-flex py-1 align-items-center">
                                            <span class="avatar me-2" style="background-image: url({{ 'https://www.gravatar.com/avatar/' ~ thread.customer.email|lower|md5 ~ '?d=identicon' }})"></span>
                                            <div class="flex-fill">
                                                <div class="font-weight-medium">{{ thread.customer.fullName }}</div>
                                                <div class="text-muted">{{ thread.customer.email }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>{{ thread.subject }}</div>
                                        <small class="text-muted">{{ thread.messageCount }} messages</small>
                                    </td>
                                    <td>
                                        {% if thread.relatedOrder %}
                                            <a href="#" class="text-decoration-none">
                                                {{ thread.relatedOrder.orderId }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">No order</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if thread.lastMessageAt %}
                                            <div>{{ thread.lastMessageAt|date('Y-m-d H:i') }}</div>
                                            <small class="text-muted">{{ thread.lastMessageAt|date_diff }}</small>
                                        {% else %}
                                            <span class="text-muted">No messages</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if thread.isOpen %}
                                            <span class="badge bg-success">Open</span>
                                        {% elseif thread.isTemporarilyClosed %}
                                            <span class="badge bg-warning">Temp. Closed</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Closed</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ path('user_service_thread_view', {id: thread.id}) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="ti ti-eye"></i>
                                                View
                                            </a>
                                            {% if thread.isOpen %}
                                                <button type="button" 
                                                        class="btn btn-sm btn-outline-warning"
                                                        data-controller="thread-action"
                                                        data-thread-action-url-value="{{ path('user_service_thread_close_temporarily', {id: thread.id}) }}">
                                                    <i class="ti ti-pause"></i>
                                                    Pause
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% else %}
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="empty">
                                            <p class="empty-title">No open threads</p>
                                            <p class="empty-subtitle text-muted">
                                                All conversations are closed or there are no active threads
                                            </p>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card" id="unassigned">
                <div class="card-header">
                    <h3 class="card-title">Unassigned Messages</h3>
                    <div class="card-actions">
                        <span class="badge bg-warning">{{ unassigned_messages|length }}</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% for message in unassigned_messages %}
                        <div class="list-group-item" data-controller="unassigned-message" data-message-id="{{ message.id }}">
                            <div class="row align-items-center">
                                <div class="col">
                                    <strong>{{ message.fromEmail }}</strong>
                                    <div class="text-muted">{{ message.subject|slice(0, 50) }}{% if message.subject|length > 50 %}...{% endif %}</div>
                                    <small class="text-muted">{{ message.createdAt|date('Y-m-d H:i') }}</small>
                                </div>
                                <div class="col-auto">
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                type="button" 
                                                data-bs-toggle="dropdown">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" 
                                                   href="#"
                                                   data-action="create-thread"
                                                   data-message-id="{{ message.id }}">
                                                    <i class="ti ti-plus"></i>
                                                    Create Thread
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" 
                                                   href="#"
                                                   data-action="assign-thread"
                                                   data-message-id="{{ message.id }}">
                                                    <i class="ti ti-link"></i>
                                                    Assign to Thread
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="empty p-4">
                            <p class="empty-title">No unassigned messages</p>
                            <p class="empty-subtitle text-muted">
                                All messages have been assigned to threads
                            </p>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
