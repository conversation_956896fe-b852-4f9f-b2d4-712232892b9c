<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-print-none">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            <h2 class="page-title">{{ title }}</h2>
                        </div>
                        <div class="col-auto ms-auto d-print-none">
                            <a href="{{ path('user_service_threads') }}" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left"></i>
                                Back to threads
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Messages ({{ messages|length }})</h3>
                    <div class="card-actions">
                        {% if thread.isOpen %}
                            <button type="button" 
                                    class="btn btn-sm btn-warning"
                                    data-controller="thread-action"
                                    data-thread-action-url-value="{{ path('user_service_thread_close_temporarily', {id: thread.id}) }}">
                                <i class="ti ti-pause"></i>
                                Close Temporarily
                            </button>
                        {% elseif thread.isTemporarilyClosed %}
                            <button type="button" 
                                    class="btn btn-sm btn-success"
                                    data-controller="thread-action"
                                    data-thread-action-url-value="{{ path('user_service_thread_reopen', {id: thread.id}) }}">
                                <i class="ti ti-play"></i>
                                Reopen
                            </button>
                        {% endif %}
                        
                        {% if not thread.isPermanentlyClosed %}
                            <button type="button" 
                                    class="btn btn-sm btn-danger"
                                    data-controller="thread-action"
                                    data-thread-action-url-value="{{ path('user_service_thread_close_permanently', {id: thread.id}) }}">
                                <i class="ti ti-x"></i>
                                Close Permanently
                            </button>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body p-0">
                    {% for message in messages %}
                        <div class="list-group-item">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <span class="avatar" style="background-image: url({{ message.fromEmail|gravatar }})"></span>
                                </div>
                                <div class="col">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>{{ message.fromName ?: message.fromEmail }}</strong>
                                            {% if message.isIncoming %}
                                                <span class="badge bg-blue ms-2">Incoming</span>
                                            {% else %}
                                                <span class="badge bg-green ms-2">Outgoing</span>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">{{ message.createdAt|date('Y-m-d H:i') }}</small>
                                    </div>
                                    <div class="text-muted mt-1">{{ message.subject }}</div>
                                    <div class="mt-2">{{ message.body|nl2br }}</div>
                                    {% if message.hasAttachments %}
                                        <div class="mt-2">
                                            <i class="ti ti-paperclip"></i>
                                            {{ message.attachments|length }} attachment(s)
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="empty p-4">
                            <p class="empty-title">No messages in this thread</p>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Thread Information</h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Subject:</strong><br>
                        {{ thread.subject }}
                    </div>
                    
                    <div class="mb-3">
                        <strong>Status:</strong><br>
                        {% if thread.isOpen %}
                            <span class="badge bg-success">Open</span>
                        {% elseif thread.isTemporarilyClosed %}
                            <span class="badge bg-warning">Temporarily Closed</span>
                        {% else %}
                            <span class="badge bg-secondary">Permanently Closed</span>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <strong>Customer:</strong><br>
                        <div class="d-flex align-items-center">
                            <span class="avatar avatar-sm me-2" style="background-image: url({{ thread.customer.email|gravatar }})"></span>
                            <div>
                                <div>{{ thread.customer.fullName }}</div>
                                <small class="text-muted">{{ thread.customer.email }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Related Order:</strong><br>
                        {% if thread.relatedOrder %}
                            <a href="#" class="text-decoration-none">{{ thread.relatedOrder.orderId }}</a>
                        {% else %}
                            <span class="text-muted">No order assigned</span>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <strong>Created:</strong><br>
                        {{ thread.createdAt|date('Y-m-d H:i') }}
                    </div>
                    
                    <div class="mb-3">
                        <strong>Last Message:</strong><br>
                        {% if thread.lastMessageAt %}
                            {{ thread.lastMessageAt|date('Y-m-d H:i') }}<br>
                            <small class="text-muted">{{ thread.lastMessageAt|date_diff }}</small>
                        {% else %}
                            <span class="text-muted">No messages</span>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">Customer Orders</h3>
                </div>
                <div class="card-body p-0">
                    {% for order in customer_orders %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ order.orderId }}</strong><br>
                                    <small class="text-muted">{{ order.dateAdd|date('Y-m-d') }}</small>
                                </div>
                                <div>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-primary"
                                            data-controller="assign-order"
                                            data-assign-order-url-value="{{ path('user_service_thread_assign_order', {id: thread.id}) }}"
                                            data-assign-order-id-value="{{ order.id }}">
                                        Assign
                                    </button>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="empty p-3">
                            <p class="empty-title">No orders found</p>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
