<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-print-none">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            <h2 class="page-title">{{ title }}</h2>
                        </div>
                        <div class="col-auto ms-auto d-print-none">
                            <a href="{{ path('user_service_customers') }}" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left"></i>
                                Back to customers
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Customer Information</h3>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <span class="avatar avatar-lg me-3" style="background-image: url({{ customer.email|gravatar('identicon', 80) }})"></span>
                        <div>
                            <h4 class="mb-0">{{ customer.fullName }}</h4>
                            <div class="text-muted">{{ customer.email }}</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-2">
                                <strong>Phone:</strong><br>
                                {{ customer.phone ?: 'Not provided' }}
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-2">
                                <strong>Status:</strong><br>
                                {% if customer.isActive %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactive</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-2">
                                <strong>Created:</strong><br>
                                {{ customer.createdAt|date('Y-m-d H:i') }}
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-2">
                                <strong>Updated:</strong><br>
                                {{ customer.updatedAt|date('Y-m-d H:i') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Orders ({{ orders|length }})</h3>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-vcenter card-table">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in orders %}
                                        <tr>
                                            <td>{{ order.orderId }}</td>
                                            <td>{{ order.dateAdd|date('Y-m-d') }}</td>
                                            <td>
                                                <span class="badge bg-info">{{ order.orderStatusId }}</span>
                                            </td>
                                            <td>{{ order.totalPriceBrutto }}</td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td colspan="4" class="text-center py-3">
                                                <div class="text-muted">No orders found</div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Communication Threads ({{ threads|length }})</h3>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-vcenter card-table">
                                <thead>
                                    <tr>
                                        <th>Subject</th>
                                        <th>Status</th>
                                        <th>Messages</th>
                                        <th>Last Activity</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for thread in threads %}
                                        <tr>
                                            <td>{{ thread.subject }}</td>
                                            <td>
                                                {% if thread.isOpen %}
                                                    <span class="badge bg-success">Open</span>
                                                {% elseif thread.isTemporarilyClosed %}
                                                    <span class="badge bg-warning">Temp. Closed</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Closed</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ thread.messageCount }}</td>
                                            <td>
                                                {% if thread.lastMessageAt %}
                                                    {{ thread.lastMessageAt|date_diff }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ path('user_service_thread_view', {id: thread.id}) }}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    View
                                                </a>
                                            </td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td colspan="5" class="text-center py-3">
                                                <div class="text-muted">No communication threads found</div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
