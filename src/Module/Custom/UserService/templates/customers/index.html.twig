<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-print-none">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            <h2 class="page-title">{{ title }}</h2>
                            <div class="text-muted mt-1">Manage customers and their communication history</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="table-responsive">
                    <table class="table table-vcenter card-table">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Orders</th>
                                <th>Threads</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers %}
                                <tr>
                                    <td>
                                        <div class="d-flex py-1 align-items-center">
                                            <span class="avatar me-2" style="background-image: url({{ customer.email|gravatar }})"></span>
                                            <div class="flex-fill">
                                                <div class="font-weight-medium">{{ customer.fullName }}</div>
                                                <div class="text-muted">{{ customer.email }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ customer.email }}</td>
                                    <td>{{ customer.phone ?: '-' }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ customer.orders|length }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">0</span>
                                    </td>
                                    <td>
                                        <div>{{ customer.createdAt|date('Y-m-d') }}</div>
                                        <small class="text-muted">{{ customer.createdAt|date_diff }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ path('user_service_customer_view', {id: customer.id}) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="ti ti-eye"></i>
                                                View
                                            </a>
                                            
                                            {% if customer.isActive %}
                                                <button type="button" 
                                                        class="btn btn-sm btn-outline-warning"
                                                        data-controller="customer-action"
                                                        data-customer-action-url-value="{{ path('user_service_customer_deactivate', {id: customer.id}) }}">
                                                    <i class="ti ti-user-off"></i>
                                                    Deactivate
                                                </button>
                                            {% else %}
                                                <button type="button" 
                                                        class="btn btn-sm btn-outline-success"
                                                        data-controller="customer-action"
                                                        data-customer-action-url-value="{{ path('user_service_customer_activate', {id: customer.id}) }}">
                                                    <i class="ti ti-user-check"></i>
                                                    Activate
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="empty">
                                            <div class="empty-img">
                                                <img src="{{ asset('static/illustrations/undraw_people_re_8spw.svg') }}" height="128" alt="">
                                            </div>
                                            <p class="empty-title">No customers found</p>
                                            <p class="empty-subtitle text-muted">
                                                Customers will appear here when orders are processed
                                            </p>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
