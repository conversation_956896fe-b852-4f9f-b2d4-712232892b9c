<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">{{ title }}</h1>
                <div class="page-subtitle">Manage customer communications and email accounts</div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="subheader">Open Threads</div>
                        <div class="ms-auto">
                            <span class="badge bg-primary">{{ open_threads_count }}</span>
                        </div>
                    </div>
                    <div class="h1 mb-3">{{ open_threads_count }}</div>
                    <div class="d-flex mb-2">
                        <div>Active conversations requiring attention</div>
                    </div>
                    <div class="progress progress-sm">
                        <div class="progress-bar bg-primary" style="width: {{ open_threads_count > 0 ? '100' : '0' }}%" role="progressbar"></div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ path('user_service_threads') }}" class="btn btn-primary btn-sm">
                        <i class="ti ti-message-circle"></i>
                        View Threads
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="subheader">Unassigned Messages</div>
                        <div class="ms-auto">
                            <span class="badge bg-warning">{{ unassigned_messages_count }}</span>
                        </div>
                    </div>
                    <div class="h1 mb-3">{{ unassigned_messages_count }}</div>
                    <div class="d-flex mb-2">
                        <div>Messages waiting for thread assignment</div>
                    </div>
                    <div class="progress progress-sm">
                        <div class="progress-bar bg-warning" style="width: {{ unassigned_messages_count > 0 ? '100' : '0' }}%" role="progressbar"></div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ path('user_service_threads') }}#unassigned" class="btn btn-warning btn-sm">
                        <i class="ti ti-inbox"></i>
                        Process Messages
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="subheader">Email Accounts</div>
                        <div class="ms-auto">
                            <span class="badge bg-success">{{ email_accounts_count }}</span>
                        </div>
                    </div>
                    <div class="h1 mb-3">{{ email_accounts_count }}</div>
                    <div class="d-flex mb-2">
                        <div>Configured email accounts</div>
                    </div>
                    <div class="progress progress-sm">
                        <div class="progress-bar bg-success" style="width: {{ email_accounts_count > 0 ? '100' : '0' }}%" role="progressbar"></div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ path('user_service_email_accounts') }}" class="btn btn-success btn-sm">
                        <i class="ti ti-mail"></i>
                        Manage Accounts
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ path('user_service_email_accounts_new') }}" class="btn btn-outline-primary w-100 mb-2">
                                <i class="ti ti-plus"></i>
                                Add Email Account
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ path('user_service_customers') }}" class="btn btn-outline-info w-100 mb-2">
                                <i class="ti ti-users"></i>
                                View Customers
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ path('user_service_threads') }}" class="btn btn-outline-warning w-100 mb-2">
                                <i class="ti ti-message-circle"></i>
                                View All Threads
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-success w-100 mb-2" data-controller="email-fetch">
                                <i class="ti ti-refresh"></i>
                                Fetch New Messages
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Auto-refresh dashboard every 30 seconds
    setInterval(function() {
        location.reload();
    }, 30000);
</script>
