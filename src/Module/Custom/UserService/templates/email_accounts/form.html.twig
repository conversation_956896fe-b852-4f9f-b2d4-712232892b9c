<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-print-none">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            <h2 class="page-title">{{ title }}</h2>
                        </div>
                        <div class="col-auto ms-auto d-print-none">
                            <a href="{{ path('user_service_email_accounts') }}" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left"></i>
                                Back to list
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <form method="post" data-controller="email-account-form">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Basic Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label required">Email Address</label>
                                    <input type="email" 
                                           class="form-control" 
                                           name="email_address" 
                                           value="{{ account ? account.emailAddress : '' }}" 
                                           required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label required">Sender Name</label>
                                    <input type="text" 
                                           class="form-control" 
                                           name="sender_name" 
                                           value="{{ account ? account.senderName : '' }}" 
                                           required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Full Name</label>
                                    <input type="text" 
                                           class="form-control" 
                                           name="full_name" 
                                           value="{{ account ? account.fullName : '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Domain</label>
                                    <input type="text" 
                                           class="form-control" 
                                           name="domain" 
                                           value="{{ account ? account.domain : '' }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h3 class="card-title">SMTP Configuration</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label required">SMTP Server</label>
                                    <input type="text" 
                                           class="form-control" 
                                           name="smtp_address" 
                                           value="{{ account ? account.smtpAddress : '' }}" 
                                           required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label required">Port</label>
                                    <input type="number" 
                                           class="form-control" 
                                           name="smtp_port" 
                                           value="{{ account ? account.smtpPort : '587' }}" 
                                           required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Security</label>
                                    <select class="form-select" name="smtp_security">
                                        <option value="tls" {{ account and account.smtpSecurity == 'tls' ? 'selected' : '' }}>TLS</option>
                                        <option value="ssl" {{ account and account.smtpSecurity == 'ssl' ? 'selected' : '' }}>SSL</option>
                                        <option value="none" {{ account and account.smtpSecurity == 'none' ? 'selected' : '' }}>None</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label required">Username</label>
                                    <input type="text" 
                                           class="form-control" 
                                           name="smtp_login" 
                                           value="{{ account ? account.smtpLogin : '' }}" 
                                           required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label required">Password</label>
                                    <input type="password" 
                                           class="form-control" 
                                           name="smtp_password" 
                                           value="{{ account ? account.smtpPassword : '' }}" 
                                           required>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h3 class="card-title">IMAP Configuration</h3>
                        <div class="card-actions">
                            <label class="form-check form-switch">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       name="imap_enabled" 
                                       value="1"
                                       {{ account and account.imapEnabled ? 'checked' : '' }}
                                       data-email-account-form-target="imapToggle">
                                <span class="form-check-label">Enable IMAP</span>
                            </label>
                        </div>
                    </div>
                    <div class="card-body" data-email-account-form-target="imapConfig">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">IMAP Server</label>
                                    <input type="text" 
                                           class="form-control" 
                                           name="imap_host" 
                                           value="{{ account ? account.imapHost : '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Port</label>
                                    <input type="number" 
                                           class="form-control" 
                                           name="imap_port" 
                                           value="{{ account ? account.imapPort : '993' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Security</label>
                                    <select class="form-select" name="imap_security">
                                        <option value="ssl" {{ account and account.imapSecurity == 'ssl' ? 'selected' : '' }}>SSL</option>
                                        <option value="tls" {{ account and account.imapSecurity == 'tls' ? 'selected' : '' }}>TLS</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Username</label>
                                    <input type="text" 
                                           class="form-control" 
                                           name="imap_username" 
                                           value="{{ account ? account.imapUsername : '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Password</label>
                                    <input type="password" 
                                           class="form-control" 
                                           name="imap_password" 
                                           value="{{ account ? account.imapPassword : '' }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary">
                            <i class="ti ti-device-floppy"></i>
                            {{ account ? 'Update' : 'Create' }} Account
                        </button>
                        <a href="{{ path('user_service_email_accounts') }}" class="btn btn-link ms-auto">
                            Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
