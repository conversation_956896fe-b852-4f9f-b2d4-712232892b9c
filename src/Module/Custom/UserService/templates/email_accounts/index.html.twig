<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-print-none">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            <h2 class="page-title">{{ title }}</h2>
                            <div class="text-muted mt-1">Manage email accounts for customer communication</div>
                        </div>
                        <div class="col-auto ms-auto d-print-none">
                            <a href="{{ path('user_service_email_accounts_new') }}" class="btn btn-primary">
                                <i class="ti ti-plus"></i>
                                Add Email Account
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="table-responsive">
                    <table class="table table-vcenter card-table">
                        <thead>
                            <tr>
                                <th>Email Address</th>
                                <th>Sender Name</th>
                                <th>SMTP Server</th>
                                <th>IMAP Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in email_accounts %}
                                <tr>
                                    <td>
                                        <div class="d-flex py-1 align-items-center">
                                            <span class="avatar me-2" style="background-image: url({{ account.emailAddress|gravatar }})"></span>
                                            <div class="flex-fill">
                                                <div class="font-weight-medium">{{ account.emailAddress }}</div>
                                                <div class="text-muted">{{ account.fullName }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ account.senderName }}</td>
                                    <td>
                                        <div>{{ account.smtpAddress }}:{{ account.smtpPort }}</div>
                                        <small class="text-muted">{{ account.smtpSecurity|upper }}</small>
                                    </td>
                                    <td>
                                        {% if account.imapEnabled %}
                                            <span class="badge bg-success">Enabled</span>
                                            <div class="text-muted small">{{ account.imapHost }}:{{ account.imapPort }}</div>
                                        {% elseif account.hasImapConfiguration %}
                                            <span class="badge bg-warning">Configured</span>
                                            <div class="text-muted small">Not enabled</div>
                                        {% else %}
                                            <span class="badge bg-secondary">Not configured</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-primary" 
                                                    data-controller="connection-test"
                                                    data-connection-test-url-value="{{ path('user_service_email_accounts_test', {id: account.id}) }}"
                                                    data-connection-test-type-value="smtp">
                                                <i class="ti ti-send"></i>
                                                Test SMTP
                                            </button>
                                            
                                            {% if account.hasImapConfiguration %}
                                                <button type="button" 
                                                        class="btn btn-sm btn-outline-info" 
                                                        data-controller="connection-test"
                                                        data-connection-test-url-value="{{ path('user_service_email_accounts_test', {id: account.id}) }}"
                                                        data-connection-test-type-value="imap">
                                                    <i class="ti ti-inbox"></i>
                                                    Test IMAP
                                                </button>
                                            {% endif %}
                                            
                                            <a href="{{ path('user_service_email_accounts_edit', {id: account.id}) }}" 
                                               class="btn btn-sm btn-outline-secondary">
                                                <i class="ti ti-edit"></i>
                                                Edit
                                            </a>
                                            
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    data-controller="delete-confirm"
                                                    data-delete-confirm-url-value="{{ path('user_service_email_accounts_delete', {id: account.id}) }}"
                                                    data-delete-confirm-message-value="Are you sure you want to delete this email account?">
                                                <i class="ti ti-trash"></i>
                                                Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% else %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="empty">
                                            <div class="empty-img">
                                                <img src="{{ asset('static/illustrations/undraw_mailbox_re_dvds.svg') }}" height="128" alt="">
                                            </div>
                                            <p class="empty-title">No email accounts found</p>
                                            <p class="empty-subtitle text-muted">
                                                Get started by adding your first email account
                                            </p>
                                            <div class="empty-action">
                                                <a href="{{ path('user_service_email_accounts_new') }}" class="btn btn-primary">
                                                    <i class="ti ti-plus"></i>
                                                    Add your first email account
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
