<?php

namespace App\Module\Custom\UserService;

use App\Module\AbstractModule;

class UserServiceModule extends AbstractModule {

    public function __construct() {}

    public function getId(): string
    {
        return 'userServiceModule';
    }

    public function getName(): string
    {
        return 'User Service Module';
    }

    public function getDescription(): string
    {
        return 'Module to help us with user service';
    }

    public function getVersion(): string
    {
        return '1.0.0';
    }

    public function getDependencies(): array
    {
        return [];
    }

    public function getHooks(): array
    {
        return ['menu'];
    }

    public function hook_menu($menuItems) {

        return $menuItems;
    }

    public function getConfigurationSchema(): array
    {
        return [
            'show_message' => [
                'type' => 'checkbox',
                'label' => 'Show Footer Message',
                'default' => true,
                'description' => 'Display example message in page footer'
            ],
            'message_text' => [
                'type' => 'text',
                'label' => 'Custom Message',
                'default' => 'Hello from Example Module!',
                'description' => 'Custom message to display'
            ],
            'message_color' => [
                'type' => 'select',
                'label' => 'Message Color',
                'options' => [
                    'primary' => 'Blue',
                    'success' => 'Green',
                    'warning' => 'Yellow',
                    'danger' => 'Red',
                    'info' => 'Light Blue'
                ],
                'default' => 'primary'
            ]
        ];
    }

    protected function onInstall(): void
    {

    }

    protected function onEnable(): void
    {

    }

    protected function onDisable(): void
    {

    }

    protected function onUninstall(): void
    {

    }
}
