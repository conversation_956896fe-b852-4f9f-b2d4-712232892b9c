<?php

namespace App\Module\Custom\UserService\Service;

use App\Core\Entity\EmailAccount;
use App\Core\Entity\EmailMessage;
use App\Core\Repository\EmailMessageRepository;
use App\Module\Custom\UserService\ValueObject\IncomingMessage;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

readonly class EmailImapService
{
    public function __construct(
        private EmailMessageRepository $emailMessageRepository,
        private EntityManagerInterface $entityManager,
        private LoggerInterface $logger
    ) {}

    public function fetchNewMessages(EmailAccount $emailAccount): array
    {
        if (!$emailAccount->hasImapConfiguration()) {
            throw new \InvalidArgumentException('Email account does not have IMAP configuration');
        }

        try {
            $connection = $this->connectToImap($emailAccount);
            $messages = $this->getNewMessages($connection, $emailAccount);
            imap_close($connection);

            $this->logger->info('Fetched new messages from IMAP', [
                'account_id' => $emailAccount->getId()->toRfc4122(),
                'message_count' => count($messages)
            ]);

            return $messages;
        } catch (\Exception $e) {
            $this->logger->error('Failed to fetch messages from IMAP', [
                'account_id' => $emailAccount->getId()->toRfc4122(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function parseIncomingMessage(array $rawMessage, EmailAccount $emailAccount): IncomingMessage
    {
        $headers = $this->parseHeaders($rawMessage['headers'] ?? []);
        
        return new IncomingMessage(
            id: $rawMessage['uid'] ?? uniqid(),
            fromEmail: $this->extractEmail($headers['from'] ?? ''),
            fromName: $this->extractName($headers['from'] ?? ''),
            subject: $headers['subject'] ?? 'No Subject',
            body: $rawMessage['body'] ?? '',
            htmlBody: $rawMessage['html_body'] ?? '',
            receivedAt: $this->parseDate($headers['date'] ?? 'now'),
            headers: $headers,
            inReplyTo: $headers['in-reply-to'] ?? null,
            references: $headers['references'] ?? null,
            messageId: $headers['message-id'] ?? null,
            attachments: $rawMessage['attachments'] ?? []
        );
    }

    public function saveIncomingMessage(IncomingMessage $incomingMessage, EmailAccount $emailAccount): EmailMessage
    {
        $existingMessage = $this->emailMessageRepository->findByExternalMessageId($incomingMessage->id);
        
        if ($existingMessage) {
            return $existingMessage;
        }

        $emailMessage = new EmailMessage();
        $emailMessage->setExternalMessageId($incomingMessage->id);
        $emailMessage->setDirection(EmailMessage::DIRECTION_INCOMING);
        $emailMessage->setFromEmail($incomingMessage->fromEmail);
        $emailMessage->setFromName($incomingMessage->fromName);
        $emailMessage->setToEmail($emailAccount->getEmailAddress());
        $emailMessage->setToName($emailAccount->getSenderName());
        $emailMessage->setSubject($incomingMessage->subject);
        $emailMessage->setBody($incomingMessage->getPlainTextBody());
        $emailMessage->setHtmlBody($incomingMessage->htmlBody);
        $emailMessage->setHeaders($incomingMessage->headers);
        $emailMessage->setAttachments($incomingMessage->attachments);
        $emailMessage->setCreatedAt($incomingMessage->receivedAt);
        $emailMessage->setEmailAccount($emailAccount);
        $emailMessage->setInReplyTo($incomingMessage->inReplyTo);
        $emailMessage->setReferences($incomingMessage->references);

        $this->emailMessageRepository->save($emailMessage);

        $this->logger->info('Saved incoming email message', [
            'message_id' => $emailMessage->getId()->toRfc4122(),
            'external_id' => $incomingMessage->id,
            'from_email' => $incomingMessage->fromEmail
        ]);

        return $emailMessage;
    }

    public function markMessageAsRead(string $messageUid, EmailAccount $emailAccount): bool
    {
        try {
            $connection = $this->connectToImap($emailAccount);
            $result = imap_setflag_full($connection, $messageUid, '\\Seen', ST_UID);
            imap_close($connection);

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('Failed to mark message as read', [
                'account_id' => $emailAccount->getId()->toRfc4122(),
                'message_uid' => $messageUid,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    private function connectToImap(EmailAccount $emailAccount)
    {
        $connectionString = $this->buildImapConnectionString($emailAccount);
        
        $connection = imap_open(
            $connectionString,
            $emailAccount->getImapUsername(),
            $emailAccount->getImapPassword()
        );

        if (!$connection) {
            throw new \RuntimeException('Failed to connect to IMAP server: ' . imap_last_error());
        }

        return $connection;
    }

    private function getNewMessages($connection, EmailAccount $emailAccount): array
    {
        $messages = [];
        $messageCount = imap_num_msg($connection);

        for ($i = 1; $i <= $messageCount; $i++) {
            $uid = imap_uid($connection, $i);
            
            if ($this->emailMessageRepository->findByExternalMessageId((string)$uid)) {
                continue;
            }

            $headers = imap_headerinfo($connection, $i);
            $body = imap_body($connection, $i);
            $structure = imap_fetchstructure($connection, $i);

            $rawMessage = [
                'uid' => (string)$uid,
                'headers' => $this->extractHeadersFromImapHeader($headers),
                'body' => $this->getTextPart($connection, $i, $structure),
                'html_body' => $this->getHtmlPart($connection, $i, $structure),
                'attachments' => $this->getAttachments($connection, $i, $structure)
            ];

            $messages[] = $rawMessage;
        }

        return $messages;
    }

    private function buildImapConnectionString(EmailAccount $emailAccount): string
    {
        $security = $emailAccount->getImapSecurity() === 'ssl' ? '/ssl' : '/tls';
        return sprintf(
            '{%s:%d/imap%s}INBOX',
            $emailAccount->getImapHost(),
            $emailAccount->getImapPort(),
            $security
        );
    }

    private function extractHeadersFromImapHeader($headers): array
    {
        $result = [];
        
        if (isset($headers->from)) {
            $result['from'] = $headers->from[0]->mailbox . '@' . $headers->from[0]->host;
        }
        
        if (isset($headers->subject)) {
            $result['subject'] = $headers->subject;
        }
        
        if (isset($headers->date)) {
            $result['date'] = $headers->date;
        }
        
        if (isset($headers->message_id)) {
            $result['message-id'] = $headers->message_id;
        }
        
        if (isset($headers->in_reply_to)) {
            $result['in-reply-to'] = $headers->in_reply_to;
        }
        
        if (isset($headers->references)) {
            $result['references'] = $headers->references;
        }

        return $result;
    }

    private function getTextPart($connection, $messageNumber, $structure): string
    {
        if ($structure->type === 0) {
            return imap_body($connection, $messageNumber);
        }
        
        return '';
    }

    private function getHtmlPart($connection, $messageNumber, $structure): string
    {
        return '';
    }

    private function getAttachments($connection, $messageNumber, $structure): array
    {
        return [];
    }

    private function parseHeaders(array $headers): array
    {
        return array_change_key_case($headers, CASE_LOWER);
    }

    private function extractEmail(string $fromField): string
    {
        if (preg_match('/<([^>]+)>/', $fromField, $matches)) {
            return $matches[1];
        }
        
        if (filter_var($fromField, FILTER_VALIDATE_EMAIL)) {
            return $fromField;
        }
        
        return '';
    }

    private function extractName(string $fromField): ?string
    {
        if (preg_match('/^([^<]+)</', $fromField, $matches)) {
            return trim($matches[1], ' "');
        }
        
        return null;
    }

    private function parseDate(string $dateString): \DateTimeInterface
    {
        try {
            return new \DateTime($dateString);
        } catch (\Exception $e) {
            return new \DateTime();
        }
    }
}
