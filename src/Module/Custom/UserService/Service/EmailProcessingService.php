<?php

namespace App\Module\Custom\UserService\Service;

use App\Core\Entity\EmailAccount;
use App\Core\Entity\EmailMessage;
use App\Core\Repository\EmailMessageRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

readonly class EmailProcessingService
{
    public function __construct(
        private EmailImapService $imapService,
        private CustomerService $customerService,
        private ThreadManagementService $threadService,
        private EmailMessageRepository $messageRepository,
        private EntityManagerInterface $entityManager,
        private LoggerInterface $logger
    ) {}

    public function processNewMessagesForAccount(EmailAccount $emailAccount): int
    {
        if (!$emailAccount->hasImapConfiguration()) {
            $this->logger->warning('Email account does not have IMAP configuration', [
                'account_id' => $emailAccount->getId()->toRfc4122()
            ]);
            return 0;
        }

        try {
            $rawMessages = $this->imapService->fetchNewMessages($emailAccount);
            $processedCount = 0;

            foreach ($rawMessages as $rawMessage) {
                try {
                    $incomingMessage = $this->imapService->parseIncomingMessage($rawMessage, $emailAccount);
                    $emailMessage = $this->imapService->saveIncomingMessage($incomingMessage, $emailAccount);
                    
                    $this->processEmailMessage($emailMessage);
                    $processedCount++;
                    
                } catch (\Exception $e) {
                    $this->logger->error('Failed to process individual message', [
                        'account_id' => $emailAccount->getId()->toRfc4122(),
                        'raw_message_id' => $rawMessage['uid'] ?? 'unknown',
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $this->logger->info('Processed new messages for email account', [
                'account_id' => $emailAccount->getId()->toRfc4122(),
                'processed_count' => $processedCount,
                'total_fetched' => count($rawMessages)
            ]);

            return $processedCount;

        } catch (\Exception $e) {
            $this->logger->error('Failed to process messages for email account', [
                'account_id' => $emailAccount->getId()->toRfc4122(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function processEmailMessage(EmailMessage $emailMessage): void
    {
        if ($emailMessage->isOutgoing()) {
            return;
        }

        $customer = $this->customerService->findOrCreateCustomerByEmail($emailMessage->getFromEmail());

        if ($emailMessage->isReply()) {
            $existingThread = $this->threadService->findOrCreateThreadForMessage($emailMessage, $customer);
            
            if ($existingThread) {
                $this->threadService->assignMessageToThread($emailMessage, $existingThread);
                $this->logger->info('Assigned message to existing thread', [
                    'message_id' => $emailMessage->getId()->toRfc4122(),
                    'thread_id' => $existingThread->getId()->toRfc4122()
                ]);
                return;
            }
        }

        $this->logger->info('Message requires manual thread assignment', [
            'message_id' => $emailMessage->getId()->toRfc4122(),
            'customer_id' => $customer->getId()->toRfc4122(),
            'from_email' => $emailMessage->getFromEmail()
        ]);
    }

    public function processAllEnabledAccounts(): array
    {
        $emailAccounts = $this->entityManager->getRepository(EmailAccount::class)
            ->findBy(['imap_enabled' => true]);

        $results = [];

        foreach ($emailAccounts as $emailAccount) {
            try {
                $processedCount = $this->processNewMessagesForAccount($emailAccount);
                $results[$emailAccount->getId()->toRfc4122()] = [
                    'success' => true,
                    'processed_count' => $processedCount
                ];
            } catch (\Exception $e) {
                $results[$emailAccount->getId()->toRfc4122()] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    public function getUnprocessedMessagesCount(): int
    {
        return count($this->threadService->getUnassignedMessages());
    }

    public function autoAssignMessagesToThreads(): int
    {
        $unassignedMessages = $this->threadService->getUnassignedMessages();
        $assignedCount = 0;

        foreach ($unassignedMessages as $message) {
            try {
                $customer = $this->customerService->findOrCreateCustomerByEmail($message->getFromEmail());
                $thread = $this->threadService->findOrCreateThreadForMessage($message, $customer);
                
                if ($thread) {
                    $this->threadService->assignMessageToThread($message, $thread);
                    $assignedCount++;
                }
            } catch (\Exception $e) {
                $this->logger->error('Failed to auto-assign message to thread', [
                    'message_id' => $message->getId()->toRfc4122(),
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->logger->info('Auto-assigned messages to threads', [
            'assigned_count' => $assignedCount,
            'total_unassigned' => count($unassignedMessages)
        ]);

        return $assignedCount;
    }
}
