<?php

namespace App\Module\Custom\UserService\Service;

use App\Core\Entity\Order;
use App\Module\Custom\UserService\Entity\Customer;
use App\Module\Custom\UserService\Entity\EmailMessage;
use App\Module\Custom\UserService\Entity\EmailThread;
use App\Module\Custom\UserService\Repository\EmailThreadRepository;
use App\Module\Custom\UserService\Repository\EmailMessageRepository;
use App\Module\Custom\UserService\ValueObject\IncomingMessage;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

readonly class ThreadManagementService
{
    public function __construct(
        private EmailThreadRepository $emailThreadRepository,
        private EmailMessageRepository $emailMessageRepository,
        private EntityManagerInterface $entityManager,
        private LoggerInterface $logger
    ) {}

    public function createThreadFromMessage(EmailMessage $message, Customer $customer, ?Order $relatedOrder = null): EmailThread
    {
        $thread = new EmailThread();
        $thread->setSubject($message->getSubject());
        $thread->setCustomer($customer);
        $thread->setRelatedOrder($relatedOrder);
        $thread->setOriginalMessageId($message->getExternalMessageId());

        $message->setThread($thread);
        $thread->addMessage($message);

        $this->emailThreadRepository->save($thread);

        $this->logger->info('Created new email thread', [
            'thread_id' => $thread->getId()->toRfc4122(),
            'customer_id' => $customer->getId()->toRfc4122(),
            'subject' => $thread->getSubject(),
            'order_id' => $relatedOrder?->getOrderId()
        ]);

        return $thread;
    }

    public function assignMessageToThread(EmailMessage $message, EmailThread $thread): void
    {
        $message->setThread($thread);
        $thread->addMessage($message);

        $this->emailMessageRepository->save($message);

        $this->logger->info('Assigned message to thread', [
            'message_id' => $message->getId()->toRfc4122(),
            'thread_id' => $thread->getId()->toRfc4122()
        ]);
    }

    public function findOrCreateThreadForMessage(EmailMessage $message, Customer $customer): ?EmailThread
    {
        if ($message->isReply()) {
            $thread = $this->findThreadByIdentifier($message->getThreadIdentifier());
            if ($thread && $thread->getCustomer()->getId()->equals($customer->getId())) {
                return $thread;
            }
        }

        $existingThreads = $this->emailThreadRepository->findActiveThreadsForCustomer($customer);
        
        if (!empty($existingThreads)) {
            foreach ($existingThreads as $thread) {
                if ($this->isMessageRelatedToThread($message, $thread)) {
                    return $thread;
                }
            }
        }

        return null;
    }

    public function createThreadWithOrder(Customer $customer, Order $order, string $subject): EmailThread
    {
        $thread = new EmailThread();
        $thread->setSubject($subject);
        $thread->setCustomer($customer);
        $thread->setRelatedOrder($order);

        $this->emailThreadRepository->save($thread);

        $this->logger->info('Created thread with order', [
            'thread_id' => $thread->getId()->toRfc4122(),
            'customer_id' => $customer->getId()->toRfc4122(),
            'order_id' => $order->getOrderId(),
            'subject' => $subject
        ]);

        return $thread;
    }

    public function getThreadsForCustomer(Customer $customer): array
    {
        return $this->emailThreadRepository->findByCustomer($customer);
    }

    public function getThreadsForOrder(Order $order): array
    {
        return $this->emailThreadRepository->findByOrder($order);
    }

    public function getOpenThreads(): array
    {
        return $this->emailThreadRepository->findOpenThreads();
    }

    public function getUnassignedMessages(): array
    {
        return $this->emailMessageRepository->findUnassignedMessages();
    }

    public function closeThreadTemporarily(EmailThread $thread): void
    {
        $thread->closeTemporarily();
        $this->emailThreadRepository->save($thread);

        $this->logger->info('Closed thread temporarily', [
            'thread_id' => $thread->getId()->toRfc4122()
        ]);
    }

    public function closeThreadPermanently(EmailThread $thread): void
    {
        $thread->closePermanently();
        $this->emailThreadRepository->save($thread);

        $this->logger->info('Closed thread permanently', [
            'thread_id' => $thread->getId()->toRfc4122()
        ]);
    }

    public function reopenThread(EmailThread $thread): void
    {
        $thread->open();
        $this->emailThreadRepository->save($thread);

        $this->logger->info('Reopened thread', [
            'thread_id' => $thread->getId()->toRfc4122()
        ]);
    }

    public function updateThreadOrder(EmailThread $thread, ?Order $order): void
    {
        $thread->setRelatedOrder($order);
        $this->emailThreadRepository->save($thread);

        $this->logger->info('Updated thread order', [
            'thread_id' => $thread->getId()->toRfc4122(),
            'order_id' => $order?->getOrderId()
        ]);
    }

    public function getThreadMessages(EmailThread $thread): array
    {
        return $this->emailMessageRepository->findByThread($thread);
    }

    public function moveMessageToThread(EmailMessage $message, EmailThread $newThread): void
    {
        $oldThread = $message->getThread();
        
        if ($oldThread) {
            $oldThread->removeMessage($message);
        }

        $message->setThread($newThread);
        $newThread->addMessage($message);

        $this->emailMessageRepository->save($message);

        $this->logger->info('Moved message to different thread', [
            'message_id' => $message->getId()->toRfc4122(),
            'old_thread_id' => $oldThread?->getId()->toRfc4122(),
            'new_thread_id' => $newThread->getId()->toRfc4122()
        ]);
    }

    public function deleteThread(EmailThread $thread): void
    {
        $threadId = $thread->getId()->toRfc4122();
        
        foreach ($thread->getMessages() as $message) {
            $message->setThread(null);
        }

        $this->emailThreadRepository->remove($thread);

        $this->logger->info('Deleted thread', [
            'thread_id' => $threadId
        ]);
    }

    private function findThreadByIdentifier(?string $identifier): ?EmailThread
    {
        if (empty($identifier)) {
            return null;
        }

        $messages = $this->emailMessageRepository->findByThreadIdentifier($identifier);
        
        foreach ($messages as $message) {
            if ($message->getThread()) {
                return $message->getThread();
            }
        }

        return null;
    }

    private function isMessageRelatedToThread(EmailMessage $message, EmailThread $thread): bool
    {
        $threadSubject = strtolower(trim($thread->getSubject()));
        $messageSubject = strtolower(trim($message->getSubject()));

        $threadSubject = preg_replace('/^(re:|fwd?:)\s*/i', '', $threadSubject);
        $messageSubject = preg_replace('/^(re:|fwd?:)\s*/i', '', $messageSubject);

        return $threadSubject === $messageSubject;
    }
}
