<?php

namespace App\Module\Custom\UserService\Service;

use App\Core\Entity\EmailAccount;
use App\Core\Repository\EmailAccountRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

readonly class EmailAccountManagementService
{
    public function __construct(
        private EmailAccountRepository $emailAccountRepository,
        private EntityManagerInterface $entityManager,
        private LoggerInterface $logger
    ) {}

    public function createEmailAccount(array $data): EmailAccount
    {
        $this->validateEmailAccountData($data);

        $emailAccount = new EmailAccount();
        $this->populateEmailAccount($emailAccount, $data);

        $this->entityManager->persist($emailAccount);
        $this->entityManager->flush();

        $this->logger->info('Created new email account', [
            'account_id' => $emailAccount->getId()->toRfc4122(),
            'email_address' => $emailAccount->getEmailAddress()
        ]);

        return $emailAccount;
    }

    public function updateEmailAccount(EmailAccount $emailAccount, array $data): EmailAccount
    {
        $this->validateEmailAccountData($data, $emailAccount);
        $this->populateEmailAccount($emailAccount, $data);

        $this->entityManager->flush();

        $this->logger->info('Updated email account', [
            'account_id' => $emailAccount->getId()->toRfc4122(),
            'email_address' => $emailAccount->getEmailAddress()
        ]);

        return $emailAccount;
    }

    public function deleteEmailAccount(EmailAccount $emailAccount): void
    {
        $accountId = $emailAccount->getId()->toRfc4122();
        $emailAddress = $emailAccount->getEmailAddress();

        $this->entityManager->remove($emailAccount);
        $this->entityManager->flush();

        $this->logger->info('Deleted email account', [
            'account_id' => $accountId,
            'email_address' => $emailAddress
        ]);
    }

    public function getAllEmailAccounts(): array
    {
        return $this->emailAccountRepository->findAll();
    }

    public function getEmailAccountsWithImap(): array
    {
        return $this->emailAccountRepository->findBy(['imap_enabled' => true]);
    }

    public function testSmtpConnection(EmailAccount $emailAccount): bool
    {
        try {
            $transport = \Symfony\Component\Mailer\Transport::fromDsn(
                $this->buildSmtpDsn($emailAccount)
            );
            
            $transport->start();
            $transport->stop();
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('SMTP connection test failed', [
                'account_id' => $emailAccount->getId()->toRfc4122(),
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function testImapConnection(EmailAccount $emailAccount): bool
    {
        if (!$emailAccount->hasImapConfiguration()) {
            return false;
        }

        try {
            $connection = imap_open(
                $this->buildImapConnectionString($emailAccount),
                $emailAccount->getImapUsername(),
                $emailAccount->getImapPassword()
            );

            if ($connection) {
                imap_close($connection);
                return true;
            }

            return false;
        } catch (\Exception $e) {
            $this->logger->error('IMAP connection test failed', [
                'account_id' => $emailAccount->getId()->toRfc4122(),
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function enableImapForAccount(EmailAccount $emailAccount): void
    {
        if (!$emailAccount->hasImapConfiguration()) {
            throw new \InvalidArgumentException('IMAP configuration is incomplete');
        }

        $emailAccount->setImapEnabled(true);
        $this->entityManager->flush();

        $this->logger->info('Enabled IMAP for email account', [
            'account_id' => $emailAccount->getId()->toRfc4122()
        ]);
    }

    public function disableImapForAccount(EmailAccount $emailAccount): void
    {
        $emailAccount->setImapEnabled(false);
        $this->entityManager->flush();

        $this->logger->info('Disabled IMAP for email account', [
            'account_id' => $emailAccount->getId()->toRfc4122()
        ]);
    }

    private function validateEmailAccountData(array $data, ?EmailAccount $existingAccount = null): void
    {
        $requiredFields = ['email_address', 'sender_name', 'smtp_address', 'smtp_login', 'smtp_password', 'smtp_port'];
        
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                throw new \InvalidArgumentException("Field {$field} is required");
            }
        }

        if (!filter_var($data['email_address'], FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Invalid email address format');
        }

        if (!is_numeric($data['smtp_port']) || $data['smtp_port'] < 1 || $data['smtp_port'] > 65535) {
            throw new \InvalidArgumentException('Invalid SMTP port');
        }

        if (isset($data['imap_port']) && !empty($data['imap_port'])) {
            if (!is_numeric($data['imap_port']) || $data['imap_port'] < 1 || $data['imap_port'] > 65535) {
                throw new \InvalidArgumentException('Invalid IMAP port');
            }
        }

        $existingAccount = $this->emailAccountRepository->findOneBy(['email_address' => $data['email_address']]);
        if ($existingAccount && (!$existingAccount || $existingAccount->getId() !== $existingAccount?->getId())) {
            throw new \InvalidArgumentException('Email account with this address already exists');
        }
    }

    private function populateEmailAccount(EmailAccount $emailAccount, array $data): void
    {
        $emailAccount->setEmailAddress($data['email_address']);
        $emailAccount->setSenderName($data['sender_name']);
        $emailAccount->setFullName($data['full_name'] ?? $data['sender_name']);
        $emailAccount->setSmtpAddress($data['smtp_address']);
        $emailAccount->setSmtpLogin($data['smtp_login']);
        $emailAccount->setSmtpPassword($data['smtp_password']);
        $emailAccount->setSmtpPort((int)$data['smtp_port']);
        $emailAccount->setSmtpSecurity($data['smtp_security'] ?? 'tls');
        $emailAccount->setDomain($data['domain'] ?? null);

        if (!empty($data['imap_host'])) {
            $emailAccount->setImapHost($data['imap_host']);
            $emailAccount->setImapUsername($data['imap_username'] ?? $data['smtp_login']);
            $emailAccount->setImapPassword($data['imap_password'] ?? $data['smtp_password']);
            $emailAccount->setImapPort(!empty($data['imap_port']) ? (int)$data['imap_port'] : 993);
            $emailAccount->setImapSecurity($data['imap_security'] ?? 'ssl');
            $emailAccount->setImapEnabled($data['imap_enabled'] ?? false);
        }
    }

    private function buildSmtpDsn(EmailAccount $emailAccount): string
    {
        $security = $emailAccount->getSmtpSecurity() ?: 'tls';
        return sprintf(
            'smtp://%s:%s@%s:%d?encryption=%s',
            urlencode($emailAccount->getSmtpLogin()),
            urlencode($emailAccount->getSmtpPassword()),
            $emailAccount->getSmtpAddress(),
            $emailAccount->getSmtpPort(),
            $security
        );
    }

    private function buildImapConnectionString(EmailAccount $emailAccount): string
    {
        $security = $emailAccount->getImapSecurity() === 'ssl' ? '/ssl' : '/tls';
        return sprintf(
            '{%s:%d/imap%s}INBOX',
            $emailAccount->getImapHost(),
            $emailAccount->getImapPort(),
            $security
        );
    }
}
