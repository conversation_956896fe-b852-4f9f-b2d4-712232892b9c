<?php

namespace App\Module\Custom\UserService\Service;

use App\Core\Entity\Customer;
use App\Core\Entity\Order;
use App\Core\Repository\CustomerRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

readonly class CustomerService
{
    public function __construct(
        private CustomerRepository $customerRepository,
        private EntityManagerInterface $entityManager,
        private LoggerInterface $logger
    ) {}

    public function findOrCreateCustomerFromOrder(Order $order): Customer
    {
        $email = $order->getEmail();
        
        if (empty($email)) {
            throw new \InvalidArgumentException('Order must have an email address');
        }

        $customer = $this->customerRepository->findByEmail($email);
        
        if ($customer === null) {
            $customer = $this->createCustomerFromOrder($order);
            $this->logger->info('Created new customer from order', [
                'customer_id' => $customer->getId()->toRfc4122(),
                'email' => $email,
                'order_id' => $order->getOrderId()
            ]);
        } else {
            $this->updateCustomerFromOrder($customer, $order);
        }

        $customer->addOrder($order);
        $this->customerRepository->save($customer);

        return $customer;
    }

    public function findOrCreateCustomerByEmail(string $email): Customer
    {
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Valid email address is required');
        }

        $customer = $this->customerRepository->findByEmail($email);
        
        if ($customer === null) {
            $customer = new Customer();
            $customer->setEmail($email);
            $this->customerRepository->save($customer);
            
            $this->logger->info('Created new customer by email', [
                'customer_id' => $customer->getId()->toRfc4122(),
                'email' => $email
            ]);
        }

        return $customer;
    }

    public function updateCustomerFromOrder(Customer $customer, Order $order): Customer
    {
        $hasChanges = false;

        if ($customer->getFirstName() === null && $order->getOrderDelivery()?->getDeliveryFirstname()) {
            $customer->setFirstName($order->getOrderDelivery()->getDeliveryFirstname());
            $hasChanges = true;
        }

        if ($customer->getLastName() === null && $order->getOrderDelivery()?->getDeliveryLastname()) {
            $customer->setLastName($order->getOrderDelivery()->getDeliveryLastname());
            $hasChanges = true;
        }

        if ($customer->getPhone() === null && $order->getPhone()) {
            $customer->setPhone($order->getPhone());
            $hasChanges = true;
        }

        if ($hasChanges) {
            $this->customerRepository->save($customer);
            $this->logger->info('Updated customer from order data', [
                'customer_id' => $customer->getId()->toRfc4122(),
                'order_id' => $order->getOrderId()
            ]);
        }

        return $customer;
    }

    public function getCustomerOrders(Customer $customer): array
    {
        return $customer->getOrders()->toArray();
    }

    public function searchCustomers(string $searchTerm): array
    {
        if (strlen($searchTerm) < 2) {
            return [];
        }

        return $this->customerRepository->searchByEmailOrName($searchTerm);
    }

    public function getActiveCustomers(): array
    {
        return $this->customerRepository->findActiveCustomers();
    }

    public function getCustomersWithOrders(): array
    {
        return $this->customerRepository->findCustomersWithOrders();
    }

    public function deactivateCustomer(Customer $customer): void
    {
        $customer->setIsActive(false);
        $this->customerRepository->save($customer);
        
        $this->logger->info('Deactivated customer', [
            'customer_id' => $customer->getId()->toRfc4122(),
            'email' => $customer->getEmail()
        ]);
    }

    public function activateCustomer(Customer $customer): void
    {
        $customer->setIsActive(true);
        $this->customerRepository->save($customer);
        
        $this->logger->info('Activated customer', [
            'customer_id' => $customer->getId()->toRfc4122(),
            'email' => $customer->getEmail()
        ]);
    }

    private function createCustomerFromOrder(Order $order): Customer
    {
        $customer = new Customer();
        $customer->setEmail($order->getEmail());
        
        if ($order->getOrderDelivery()) {
            $delivery = $order->getOrderDelivery();
            $customer->setFirstName($delivery->getDeliveryFirstname());
            $customer->setLastName($delivery->getDeliveryLastname());
        }
        
        if ($order->getPhone()) {
            $customer->setPhone($order->getPhone());
        }

        return $customer;
    }
}
