<?php

namespace App\Core\Entity;

use App\Core\Repository\OrderRepository;
use App\Core\Taxonomy\DateTaxonomy;
use App\Core\Utility\Money;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Serializer\Annotation\MaxDepth;
use Symfony\Component\Serializer\Attribute\Context;
use Symfony\Component\Serializer\Attribute\Ignore;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: OrderRepository::class)]
#[ORM\Table(name: '`order`')]
class Order {
    
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;
    
    #[ORM\Column(length: 50, unique: true)]
    private ?string $order_id = null;
    
    #[ORM\Column]
    private ?int $shop_order_id = null;
    
    #[ORM\Column(length: 50)]
    private ?string $external_order_id = null;
    
    #[ORM\Column(length: 20)]
    private ?string $order_source = null;
    
    #[ORM\Column]
    private ?int $order_source_id = null;
    
    #[ORM\Column(length: 200)]
    private ?string $order_source_info = null;
    
    #[ORM\Column]
    private ?int $order_status_id = null;
    
    #[ORM\Column(length: 255)]
    #[Context([DateTimeNormalizer::FORMAT_KEY => DateTaxonomy::DATE_FORMAT])]
    private ?\DateTimeImmutable $date_add = null;
    
    #[ORM\Column(length: 255)]
    #[Context([DateTimeNormalizer::FORMAT_KEY => DateTaxonomy::DATE_FORMAT])]
    private ?\DateTimeImmutable $date_confirmed = null;
    
    #[ORM\Column(length: 255)]
    #[Context([DateTimeNormalizer::FORMAT_KEY => DateTaxonomy::DATE_FORMAT])]
    private ?\DateTimeImmutable $date_in_status = null;
    
    #[ORM\Column]
    private ?bool $confirmed = null;
    
    #[ORM\Column(length: 100)]
    private ?string $user_login = null;
    
    #[ORM\Column(length: 3)]
    private ?string $currency = null;
    
    #[ORM\Column(length: 100)]
    private ?string $payment_method = null;
    
    #[ORM\Column(length: 1)]
    private ?string $payment_method_cod = null;
    
    #[ORM\Column]
    private ?int $payment_done = null;
    
    #[ORM\Column(type: Types::TEXT)]
    private ?string $user_comments = null;
    
    #[ORM\Column(type: Types::TEXT)]
    private ?string $admin_comments = null;
    
    #[ORM\Column(length: 150)]
    private ?string $email = null;
    
    #[ORM\Column(length: 100)]
    private ?string $phone = null;
    
    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $extra_field_1 = null;
    
    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $extra_field_2 = null;
    
    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $custom_extra_fields = null;
    
    #[ORM\Column(length: 150)]
    private ?string $order_page = null;
    
    #[ORM\Column]
    private ?int $pick_state = null;
    
    #[ORM\Column]
    private ?int $pack_state = null;
    
    #[ORM\OneToMany(targetEntity: OrderProduct::class, mappedBy: 'order_id', cascade: [
        'persist',
        'remove'
    ], orphanRemoval: true)]
    #[MaxDepth(2)]
    private Collection $products;
    
    #[ORM\ManyToOne(fetch:'EAGER')]
    private ?OrderStatus $internal_status_id = null;
    
    #[ORM\OneToOne(mappedBy: 'order', cascade: [
        'persist',
        'remove'
    ])]
    private ?OrderDelivery $orderDelivery = null;
    
    #[ORM\OneToOne(mappedBy: 'order', cascade: [
        'persist',
        'remove'
    ])]
    private ?OrderInvoice $orderInvoice = null;
    
    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $status_date = null;
    
    #[ORM\Column(length: 20, nullable: true)]
    private ?int $discount_value = null;
    
    #[ORM\Column(length: 255, nullable: true)]
    private ?string $discount_name = null;
    
    #[ORM\Column(length: 20, nullable: true)]
    private ?int $full_price = null;
    
    #[Ignore]
    #[ORM\OneToMany(targetEntity: ProductReservation::class, mappedBy: 'id_order', cascade: ['remove'], orphanRemoval: true)]
    private Collection $reservedProducts;

    #[ORM\ManyToOne(fetch: 'EAGER', inversedBy: 'orders')]
    private ?OrderFlag $markFlag = null;


    #[ORM\OneToMany(targetEntity: OrderHistoryChangeReview::class, mappedBy: 'parent')]
    private Collection $history;

    #[ORM\Column]
    private ?bool $paid = null;

    #[ORM\Column]
    private ?bool $cancelled = null;

    #[ORM\ManyToOne(targetEntity: Customer::class, inversedBy: 'orders')]
    #[ORM\JoinColumn(nullable: true)]
    private ?Customer $customer = null;

    public function __construct() {
        $this->products = new ArrayCollection();
        $this->history = new ArrayCollection();
    }
    public function getId(): ?Uuid {
        return $this->id;
    }
    
    public function getOrderId(): ?string {
        return $this->order_id;
    }
    
    public function setOrderId(string $order_id): static {
        $this->order_id = $order_id;
        
        return $this;
    }
    
    public function getShopOrderId(): ?int {
        return $this->shop_order_id;
    }
    
    public function setShopOrderId(int $shop_order_id): static {
        $this->shop_order_id = $shop_order_id;
        
        return $this;
    }
    
    public function getExternalOrderId(): ?string {
        return $this->external_order_id;
    }
    
    public function setExternalOrderId(string $external_order_id): static {
        $this->external_order_id = $external_order_id;
        
        return $this;
    }
    
    public function getOrderSource(): ?string {
        return $this->order_source;
    }
    
    public function setOrderSource(string $order_source): static {
        $this->order_source = $order_source;
        
        return $this;
    }
    
    public function getOrderSourceId(): ?int {
        return $this->order_source_id;
    }
    
    public function setOrderSourceId(int $order_source_id): static {
        $this->order_source_id = $order_source_id;
        
        return $this;
    }
    
    public function getOrderSourceInfo(): ?string {
        return $this->order_source_info;
    }
    
    public function setOrderSourceInfo(string $order_source_info): static {
        $this->order_source_info = $order_source_info;
        
        return $this;
    }
    
    public function getOrderStatusId(): ?int {
        return $this->order_status_id;
    }
    
    public function setOrderStatusId(int $order_status_id): static {
        $this->order_status_id = $order_status_id;
        
        return $this;
    }
    
    public function getDateAdd(): ?\DateTimeImmutable {
        return $this->date_add;
    }
    
    public function setDateAdd(?\DateTimeImmutable $date_add): static {
        $this->date_add = $date_add;
        
        return $this;
    }
    
    public function getDateConfirmed(): ?\DateTimeImmutable {
        return $this->date_confirmed;
    }
    
    public function setDateConfirmed(?\DateTimeImmutable $date_confirmed): static {
        $this->date_confirmed = $date_confirmed;
        
        return $this;
    }
    
    public function getDateInStatus(): ?\DateTimeImmutable {
        return $this->date_in_status;
    }
    
    public function setDateInStatus(?\DateTimeImmutable $date_in_status): static {
        $this->date_in_status = $date_in_status;
        
        return $this;
    }
    
    public function isConfirmed(): ?bool {
        return $this->confirmed;
    }
    
    public function setConfirmed(bool $confirmed): static {
        $this->confirmed = $confirmed;
        
        return $this;
    }
    
    public function getUserLogin(): ?string {
        return $this->user_login;
    }
    
    public function setUserLogin(string $user_login): static {
        $this->user_login = $user_login;
        
        return $this;
    }
    
    public function getCurrency(): ?string {
        return $this->currency;
    }
    
    public function setCurrency(string $currency): static {
        $this->currency = $currency;
        
        return $this;
    }
    
    public function getPaymentMethod(): ?string {
        return $this->payment_method;
    }
    
    public function setPaymentMethod(string $payment_method): static {
        $this->payment_method = $payment_method;
        
        return $this;
    }
    
    public function getPaymentMethodCod(): ?string {
        return $this->payment_method_cod;
    }
    
    public function setPaymentMethodCod(string $payment_method_cod): static {
        $this->payment_method_cod = $payment_method_cod;
        
        return $this;
    }
    
    public function getPaymentDone(): ?int {
        return $this->payment_done;
    }
    
    public function setPaymentDone(int $payment_done): static {
        $this->payment_done = $payment_done;
        
        return $this;
    }
    
    public function getUserComments(): ?string {
        return $this->user_comments;
    }
    
    public function setUserComments(string $user_comments): static {
        $this->user_comments = $user_comments;
        
        return $this;
    }
    
    public function getAdminComments(): ?string {
        return $this->admin_comments;
    }
    
    public function setAdminComments(string $admin_comments): static {
        $this->admin_comments = $admin_comments;
        
        return $this;
    }
    
    public function getEmail(): ?string {
        return $this->email;
    }
    
    public function setEmail(string $email): static {
        $this->email = $email;
        
        return $this;
    }
    
    public function getPhone(): ?string {
        return $this->phone;
    }
    
    public function setPhone(string $phone): static {
        $this->phone = $phone;
        
        return $this;
    }
    
    public function getExtraField1(): ?string {
        return $this->extra_field_1;
    }
    
    public function setExtraField1(string $extra_field_1): static {
        $this->extra_field_1 = $extra_field_1;
        
        return $this;
    }
    
    public function getExtraField2(): ?string {
        return $this->extra_field_2;
    }
    
    public function setExtraField2(string $extra_field_2): static {
        $this->extra_field_2 = $extra_field_2;
        
        return $this;
    }
    
    public function getCustomExtraFields(): ?array {
        return $this->custom_extra_fields;
    }
    
    public function setCustomExtraFields(?array $custom_extra_fields): static {
        $this->custom_extra_fields = $custom_extra_fields;
        
        return $this;
    }
    
    public function getOrderPage(): ?string {
        return $this->order_page;
    }
    
    public function setOrderPage(string $order_page): static {
        $this->order_page = $order_page;
        
        return $this;
    }
    
    public function getPickState(): ?int {
        return $this->pick_state;
    }
    
    public function setPickState(int $pick_state): static {
        $this->pick_state = $pick_state;
        
        return $this;
    }
    
    public function getPackState(): ?int {
        return $this->pack_state;
    }
    
    public function setPackState(int $pack_state): static {
        $this->pack_state = $pack_state;
        
        return $this;
    }
    
    /**
     * @return Collection<int, OrderProduct>
     */
    public function getProducts(): Collection {
        return $this->products;
    }
    
    public function getTotalOrderValue(): int {
        $total = 0;
        /** @var OrderProduct $product */
        foreach ($this->getProducts() as $product) {
            $total += $product->getPriceBrutto() * $product->getQuantity();
        }
        
        return $total;
    }
    
    public function getFullPriceCountByProducts(): int {
        $total = new Money(0);
        foreach ($this->getProducts() as $product) {
            $total = $total->add(new Money($product->getPriceBrutto() * $product->getQuantity()));
        }
        
        return $total->getAmount();
    }
    
    public function addProduct(OrderProduct $product): static {
        if (!$this->products->contains($product)) {
            $this->products->add($product);
            $product->setOrderId($this);
        }
        
        return $this;
    }
    
    public function removeProduct(OrderProduct $product): static {
        if ($this->products->removeElement($product)) {
            if ($product->getOrderId() === $this) {
                $product->setOrderId(null);
            }
        }
        
        return $this;
    }
    
    public function getInternalStatusId(): ?OrderStatus {
        return $this->internal_status_id;
    }
    
    public function setInternalStatusId(?OrderStatus $internal_status_id): static {
        $this->internal_status_id = $internal_status_id;
        $statusDate = new \DateTimeImmutable('now', new \DateTimeZone('Europe/Warsaw'));
        $statusDate->format(DateTaxonomy::DATE_FORMAT);

        $this->setStatusDate($statusDate);
        
        return $this;
    }
    
    public function getOrderDelivery(): ?OrderDelivery {
        return $this->orderDelivery;
    }
    
    public function setOrderDelivery(OrderDelivery $orderDelivery): static {
        // set the owning side of the relation if necessary
        if ($orderDelivery->getOrder() !== $this) {
            $orderDelivery->setOrder($this);
        }
        $this->orderDelivery = $orderDelivery;
        
        return $this;
    }
    
    public function getOrderInvoice(): ?OrderInvoice {
        return $this->orderInvoice;
    }
    
    public function setOrderInvoice(OrderInvoice $orderInvoice): static {
        if ($orderInvoice->getOrder() !== $this) {
            $orderInvoice->setOrder($this);
        }
        $this->orderInvoice = $orderInvoice;
        
        return $this;
    }
    
    public function getStatusDate(): ?\DateTimeImmutable {
        return $this->status_date;
    }
    
    public function setStatusDate(?\DateTimeImmutable $status_date): static {
        $this->status_date = $status_date;
        
        return $this;
    }
    
    public function getDiscountValue(): ?int {
        return $this->discount_value;
    }
    
    public function setDiscountValue(?int $discount_value): static {
        $this->discount_value = $discount_value;
        
        return $this;
    }
    
    public function getDiscountName(): ?string {
        return $this->discount_name;
    }
    
    public function setDiscountName(?string $discount_name): static {
        $this->discount_name = $discount_name;
        
        return $this;
    }
    
    public function getFullPrice(): ?int {
        return $this->full_price;
    }
    
    public function setFullPrice(?int $full_price): static {
        $this->full_price = $full_price;
        
        return $this;
    }
    
    public function getFullPriceWithDiscount(): int {
        return $this->getFullPrice() - $this->getDiscountValue();
    }

    public function getFullPriceWithDelivery(): int {
        return  ($this->getFullPriceWithDiscount() + $this->orderDelivery->getDeliveryPrice());
    }

    public function __clone() {
        if ($this->id) {
            $this->id = null;
        }
        $clonedProducts = new ArrayCollection();
        foreach ($this->products as $product) {
            $clonedProduct = clone $product;
            $clonedProduct->setOrderId($this);
            $clonedProducts->add($clonedProduct);
        }
        $this->products = $clonedProducts;
    }

    public function getWeight(): float {
        $weight = 0;

        foreach ($this->getProducts() as $product) {
            $weight += ($product->getQuantity() * $product->getWeight());
        }

        return $weight;
    }

    public function getMarkFlag(): ?OrderFlag
    {
        return $this->markFlag;
    }

    public function setMarkFlag(?OrderFlag $markFlag): static
    {
        $this->markFlag = $markFlag;

        return $this;
    }

    public function getHistory(): Collection
    {
        return $this->history;
    }

    public function isPaid(): ?bool
    {
        return $this->paid;
    }

    public function setPaid(bool $paid): static
    {
        $this->paid = $paid;

        return $this;
    }

    public function isCancelled(): ?bool
    {
        return $this->cancelled;
    }

    public function setCancelled(bool $cancelled): static
    {
        $this->cancelled = $cancelled;

        return $this;
    }

    public function checkIfZKExists(): bool {
        return str_starts_with($this->getOrderInvoice()->getInvoiceNumber(), 'ZK');
    }

    public function checkIfFSExists(): bool {
        return str_starts_with($this->getOrderInvoice()->getInvoiceNumber(), 'FS');
    }

    public function getCustomer(): ?Customer
    {
        return $this->customer;
    }

    public function setCustomer(?Customer $customer): static
    {
        $this->customer = $customer;
        return $this;
    }
}
