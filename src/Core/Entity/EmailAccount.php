<?php

namespace App\Core\Entity;

use App\Core\Repository\EmailAccountRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: EmailAccountRepository::class)]
class EmailAccount
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255)]
    private ?string $smtp_address = null;

    #[ORM\Column(length: 255)]
    private ?string $smtp_login = null;

    #[ORM\Column(length: 255)]
    private ?string $smtp_password = null;

    #[ORM\Column(length: 255)]
    private ?string $email_address = null;

    #[ORM\Column(length: 255)]
    private ?string $sender_name = null;

    #[ORM\Column(length: 255)]
    private ?string $smtp_security = null;

    #[ORM\Column(length: 255)]
    private ?string $full_name = null;

    #[ORM\Column]
    private ?int $smtp_port = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $domain = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $imap_host = null;

    #[ORM\Column(nullable: true)]
    private ?int $imap_port = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $imap_security = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $imap_username = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $imap_password = null;

    #[ORM\Column]
    private bool $imap_enabled = false;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getSmtpAddress(): ?string
    {
        return $this->smtp_address;
    }

    public function setSmtpAddress(string $smtp_address): static
    {
        $this->smtp_address = $smtp_address;

        return $this;
    }

    public function getSmtpLogin(): ?string
    {
        return $this->smtp_login;
    }

    public function setSmtpLogin(string $smtp_login): static
    {
        $this->smtp_login = $smtp_login;

        return $this;
    }

    public function getSmtpPassword(): ?string
    {
        return $this->smtp_password;
    }

    public function setSmtpPassword(string $smtp_password): static
    {
        $this->smtp_password = $smtp_password;

        return $this;
    }

    public function getEmailAddress(): ?string
    {
        return $this->email_address;
    }

    public function setEmailAddress(string $email_address): static
    {
        $this->email_address = $email_address;

        return $this;
    }

    public function getSenderName(): ?string
    {
        return $this->sender_name;
    }

    public function setSenderName(string $sender_name): static
    {
        $this->sender_name = $sender_name;

        return $this;
    }

    public function getSmtpSecurity(): ?string
    {
        return $this->smtp_security;
    }

    public function setSmtpSecurity(string $smtp_security): static
    {
        $this->smtp_security = $smtp_security;

        return $this;
    }

    public function getFullName(): ?string
    {
        return $this->full_name;
    }

    public function setFullName(string $full_name): static
    {
        $this->full_name = $full_name;

        return $this;
    }

    public function getSmtpPort(): ?int
    {
        return $this->smtp_port;
    }

    public function setSmtpPort(int $smtp_port): static
    {
        $this->smtp_port = $smtp_port;

        return $this;
    }

    public function getDomain(): ?string
    {
        return $this->domain;
    }

    public function setDomain(?string $domain): static
    {
        $this->domain = $domain;

        return $this;
    }

    public function getImapHost(): ?string
    {
        return $this->imap_host;
    }

    public function setImapHost(?string $imap_host): static
    {
        $this->imap_host = $imap_host;
        return $this;
    }

    public function getImapPort(): ?int
    {
        return $this->imap_port;
    }

    public function setImapPort(?int $imap_port): static
    {
        $this->imap_port = $imap_port;
        return $this;
    }

    public function getImapSecurity(): ?string
    {
        return $this->imap_security;
    }

    public function setImapSecurity(?string $imap_security): static
    {
        $this->imap_security = $imap_security;
        return $this;
    }

    public function getImapUsername(): ?string
    {
        return $this->imap_username;
    }

    public function setImapUsername(?string $imap_username): static
    {
        $this->imap_username = $imap_username;
        return $this;
    }

    public function getImapPassword(): ?string
    {
        return $this->imap_password;
    }

    public function setImapPassword(?string $imap_password): static
    {
        $this->imap_password = $imap_password;
        return $this;
    }

    public function isImapEnabled(): bool
    {
        return $this->imap_enabled;
    }

    public function setImapEnabled(bool $imap_enabled): static
    {
        $this->imap_enabled = $imap_enabled;
        return $this;
    }

    public function hasImapConfiguration(): bool
    {
        return !empty($this->imap_host) &&
               !empty($this->imap_username) &&
               !empty($this->imap_password) &&
               $this->imap_enabled;
    }
}
