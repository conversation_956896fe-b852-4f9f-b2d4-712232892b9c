<?php

namespace App\Core;

use App\Core\Attribute\AsAutoEventCondition;
use App\Core\Attribute\AsCarrierProvider;
use App\Core\Attribute\AsCarrierType;
use App\Core\Attribute\AsAutoEventAction;
use App\Core\Attribute\AsMenuLink;
use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Component\DependencyInjection\ChildDefinition;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;
use Symfony\Component\Routing\Loader\Configurator\RoutingConfigurator;

class Kernel extends BaseKernel
{
    use MicroKernelTrait;

    public function __construct($environment, $debug) {
        date_default_timezone_set('Europe/Warsaw');
        parent::__construct($environment, $debug);
    }
    protected function build(ContainerBuilder $container): void
    {
        $container->registerAttributeForAutoconfiguration(
            AsCarrierType::class,
            static function (
                ChildDefinition  $definition,
                AsCarrierType    $element,
                \ReflectionClass $reflector
            ): void {

                $definition->addTag('carrier.type.handler', [
                    'class' => $reflector->getName()
                ]);
            }
        );

        $container->registerAttributeForAutoconfiguration(
            AsCarrierProvider::class,
            static function (
                ChildDefinition  $definition,
                AsCarrierType    $element,
                \ReflectionClass $reflector
            ): void {

                $definition->addTag('carrier.provider', [
                    'class' => $reflector->getName()
                ]);
            }
        );

        $container->registerAttributeForAutoconfiguration(
            AsAutoEventAction::class,
            static function (
                ChildDefinition  $definition,
                AsCarrierType    $element,
                \ReflectionClass $reflector
            ): void {

                $definition->addTag('auto.event.action', [
                    'class' => $reflector->getName()
                ]);
            }
        );

        $container->registerAttributeForAutoconfiguration(
            AsAutoEventCondition::class,
            static function (
                ChildDefinition  $definition,
                AsCarrierType    $element,
                \ReflectionClass $reflector
            ): void {

                $definition->addTag('auto.event.condition', [
                    'class' => $reflector->getName()
                ]);
            }
        );
        $container->registerAttributeForAutoconfiguration(
            AsMenuLink::class,
            static function (
                ChildDefinition $definition,
                AsMenuLink $menuLink,
                \Reflector $reflector
            ): void {
                $definition->addTag('menu.menu_link', [
                    'method' => $reflector->getName(),
                    'linkName' => $menuLink->getLinkName(),
                    'linkPermission' => $menuLink->getPermission(),
                    'linkParent' => $menuLink->getParent(),
                    'class' => $reflector instanceof \ReflectionMethod ? $reflector->getDeclaringClass()->getName() : '',
                ]);
            }
        );
    }
protected function configureRoutes(RoutingConfigurator $routes): void
{
    $projectDir = dirname(__DIR__, 2); // czyli katalog projektu

    $routes->import($projectDir.'/config/{routes}/'.$this->environment.'/*.yaml');
    $routes->import($projectDir.'/config/{routes}/*.yaml');
    $routes->import($projectDir.'/config/{routes}.yaml');
}
}