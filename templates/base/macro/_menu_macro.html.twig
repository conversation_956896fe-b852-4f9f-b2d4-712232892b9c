{% macro menu_macro(menu_items, depth = 0) %}
    <ul class="sidebar-menu {{ depth == 0 ? 'root-menu' : "child-menu depth-#{depth}" }} ">
        {% set current_route = app.request.attributes.get('_route') %}

        {% for item in menu_items %}
            {% if item.children is defined and item.children is not empty %}
                <li class="menu-item submenu submenu-depth-{{ depth }}">
                    <a href="{{ item.url }}"
                        class="menu-link {{ current_route == (item.route is defined) ? item.route : '' ? 'active' : '' }}">
                        <i class="{{ item.icon }}"></i>
                            {{ item.label }}
                    </a>
                    {{ _self.menu_macro(item.children, depth + 1) }}
                </li>
            {% else %}
            <li class="menu-item">
                <a href="{{ item.url }}"
                   class="menu-link {{ current_route == item.route ? 'active' : '' }}">
                    <i class="{{ item.icon }}"></i>
                    {{ item.label }}
                </a>
            </li>
            {% endif %}
        {% endfor %}
    </ul>
{% endmacro %}