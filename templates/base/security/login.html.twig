<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - {{ title ?? 'System' }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --federal-blue: #03045eff;
            --marian-blue: #023e8aff;
            --honolulu-blue: #0077b6ff;
            --blue-green: #0096c7ff;
            --pacific-cyan: #00b4d8ff;
            --vivid-sky-blue: #48cae4ff;
            --non-photo-blue: #90e0efff;
            --non-photo-blue-2: #ade8f4ff;
            --light-cyan: #caf0f8ff;
        }

        body {
            background: linear-gradient(135deg, var(--light-cyan) 0%, var(--non-photo-blue-2) 50%, var(--vivid-sky-blue) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(3, 4, 94, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .login-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(3, 4, 94, 0.2);
        }

        .login-header {
            background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 50%, var(--honolulu-blue) 100%);
            color: white;
            border-radius: 20px 20px 0 0;
        }

        .form-control {
            border-radius: 15px;
            border: 2px solid var(--non-photo-blue-2);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--pacific-cyan);
            box-shadow: 0 0 0 0.2rem rgba(0, 180, 216, 0.25);
            transform: scale(1.02);
        }

        .btn-login {
            background: linear-gradient(135deg, var(--federal-blue) 0%, var(--marian-blue) 50%, var(--honolulu-blue) 100%);
            border: none;
            border-radius: 25px;
            padding: 15px 35px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            animation: blink 0.6s ease-in-out;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(3, 4, 94, 0.4);
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card login-card">
                    <div class="card__header login-header text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-lock me-2"></i>
                            Login
                        </h3>
                        <p class="mb-0 mt-2 opacity-75">Access your account</p>
                    </div>
                    <div class="card-body p-4">
                        {% if error %}
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                {{ error.messageKey|trans(error.messageData, 'security') }}
                            </div>
                        {% endif %}

                        <form method="post">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Username
                                </label>
                                <input type="text"
                                       value="{{ last_username }}"
                                       name="_username"
                                       id="username"
                                       class="form-control form-control-lg"
                                       autocomplete="username"
                                       required
                                       autofocus
                                       placeholder="Enter your username">
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-key me-1"></i>
                                    Password
                                </label>
                                <input type="password"
                                       name="_password"
                                       id="password"
                                       class="form-control form-control-lg"
                                       autocomplete="current-password"
                                       required
                                       placeholder="Enter your password">
                            </div>

                            <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">

                            <div class="d-grid">
                                <button class="btn btn-primary btn-lg btn-login" type="submit">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Sign In
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer text-center py-3 bg-transparent">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            Secure login
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
