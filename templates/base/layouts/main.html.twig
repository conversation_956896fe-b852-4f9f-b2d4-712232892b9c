<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{ page_title|default('Dashboard') }}</title>
    <link rel="icon" href="{{ asset('images/logo.png') }}">

    {% block stylesheets %}
        <link rel="stylesheet" href="{{ asset('styles/app.css') }}">
    {% endblock %}
    
</head>
<body>

    <div class="container-fluid vh-100 p-0">
        <!-- Flash Messages -->
        {% include 'layouts/flash.messages.html.twig' %}
        <div class="row g-0 h-100">
            <!-- Left Sidebar - Fixed width -->
            <div class="col-3 col-lg-2 bg-dark text-white">
                <div class="h-100 overflow-auto">
                    {{ regions.sidebar_menu|raw }}
                </div>
            </div>

            <!-- Right Content Area -->
            <div class="col-9 col-lg-10 d-flex flex-column">
                <!-- Top Panel - 1/5 height -->
                <div class="top-panel bg-light border-bottom">
                    <div class="h-100 overflow-auto p-3">
                        {{ regions.top_panel|raw }}
                    </div>
                </div>

                <!-- Main Content - 4/5 height -->
                <div class="flex-grow-1 overflow-auto">
                    <!-- Content Before -->
                    {% if regions.content_before %}
                        <div class="p-3 border-bottom">
                            {{ regions.content_before|raw }}
                        </div>
                    {% endif %}

                    <!-- Page Content -->
                    <div class="p-3 flex-grow-1">
                        {{ regions.page_content|raw }}
                    </div>

                    <!-- Content After -->
                    {% if regions.content_after %}
                        <div class="p-3 border-top">
                            {{ regions.content_after|raw }}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    {{ importmap('app') }}

    <!-- AssetFlow Scripts -->
    {% if assetFlow.scripts is defined %}
        {% for script in assetFlow.scripts %}
            <script src="{{ asset(script.path) }}"></script>
        {% endfor %}
    {% endif %}

    <!-- AssetFlow Page Settings -->
    {% if assetFlow.pageSettings is defined %}
        <script>
            window.AssetFlow = window.AssetFlow || {};
            window.AssetFlow.settings = {{ assetFlow.pageSettings|json_encode|raw }};
        </script>
    {% endif %}

    <!-- Stimulus Controllers -->
    {% if assetFlow.stimulusControllers is defined %}
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                {% for controllerName, controllerData in assetFlow.stimulusControllers %}
                    if (window.Stimulus && window.Stimulus.application) {
                        const controller = window.Stimulus.application.getControllerForElementAndIdentifier(
                            document.querySelector('[data-controller*="{{ controllerName }}"]'),
                            '{{ controllerName }}'
                        );
                        if (controller) {
                            Object.assign(controller, {{ controllerData|json_encode|raw }});
                        }
                    }
                {% endfor %}
            });
        </script>
    {% endif %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const flashMessages = document.querySelectorAll('#flash-messages .alert');
                
                if (flashMessages.length > 0) {
                    console.log('Found ' + flashMessages.length + ' flash messages');
                    
                    flashMessages.forEach(function(alert) {
                        const isError = alert.classList.contains('alert-danger');
                        const hideDelay = isError ? 8000 : 5000;
                        
                        console.log('Setting timeout for alert:', hideDelay + 'ms');
                        
                        setTimeout(function() {
                            if (alert.parentNode) {
                                console.log('Hiding alert');
                                alert.classList.add('fade-out');
                                setTimeout(function() {
                                    if (alert.parentNode) {
                                        alert.remove();
                                        console.log('Alert removed');
                                    }
                                }, 300);
                            }
                        }, hideDelay);
                    });
                } else {
                    console.log('No flash messages found');
                }
            }, 100);
        });
    </script>
</body>
</html>
