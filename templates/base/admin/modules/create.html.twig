{% extends 'layouts/main.html.twig' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card__header">
                    <h3 class="card-title">Create New Module</h3>
                    <div class="card-tools">
                        <a href="{{ path('admin_modules') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Modules
                        </a>
                    </div>
                </div>

                <form method="post" class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="module_id">Module ID *</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="module_id" 
                                       name="module_id" 
                                       required
                                       pattern="[a-zA-Z][a-zA-Z0-9_]*"
                                       placeholder="e.g., blog, gallery, contact">
                                <small class="form-text text-muted">
                                    Must start with a letter and contain only letters, numbers, and underscores
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="module_name">Module Name *</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="module_name" 
                                       name="module_name" 
                                       required
                                       placeholder="e.g., Blog Module, Image Gallery">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="module_description">Description</label>
                        <textarea class="form-control" 
                                  id="module_description" 
                                  name="module_description" 
                                  rows="3"
                                  placeholder="Brief description of what this module does"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="module_version">Version</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="module_version" 
                                       name="module_version" 
                                       value="1.0.0"
                                       placeholder="1.0.0">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="module_hooks">Hooks (comma-separated)</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="module_hooks" 
                                       name="module_hooks" 
                                       placeholder="page_header, page_sidebar, page_footer">
                                <small class="form-text text-muted">
                                    Available regions: page_header, page_sidebar, page_content, page_footer
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card__header">
                            <h5>What will be created:</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-folder text-warning"></i> <code>src/Module/[ModuleName]/</code> - Module directory</li>
                                <li><i class="fas fa-file-code text-info"></i> <code>src/Module/[ModuleName]/[ModuleName]Module.php</code> - Main module class</li>
                                <li><i class="fas fa-folder text-warning"></i> <code>src/Module/[ModuleName]/Controller/</code> - Controllers directory</li>
                                <li><i class="fas fa-folder text-warning"></i> <code>src/Module/[ModuleName]/Service/</code> - Services directory</li>
                                <li><i class="fas fa-folder text-warning"></i> <code>src/Module/[ModuleName]/templates/</code> - Templates directory</li>
                            </ul>
                        </div>
                    </div>

                    <div class="form-group mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Module
                        </button>
                        <a href="{{ path('admin_modules') }}" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate module name from ID
    const moduleIdInput = document.getElementById('module_id');
    const moduleNameInput = document.getElementById('module_name');

    moduleIdInput.addEventListener('input', function() {
        if (!moduleNameInput.value || moduleNameInput.dataset.autoGenerated) {
            const id = this.value;
            const name = id.charAt(0).toUpperCase() + id.slice(1).replace(/_/g, ' ') + ' Module';
            moduleNameInput.value = name;
            moduleNameInput.dataset.autoGenerated = 'true';
        }
    });

    moduleNameInput.addEventListener('input', function() {
        this.dataset.autoGenerated = 'false';
    });
});
</script>
{% endblock %}
