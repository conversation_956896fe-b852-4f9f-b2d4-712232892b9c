
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card__header">
                <h3 class="card-title">{{ title }}</h3>
                <p class="text-muted">Configure module settings and options</p>
            </div>
            <div class="card-body">

                <!-- Module Info -->
                <div class="alert alert-info mb-4">
                    <h5><i class="fas fa-info-circle"></i> {{ module.name }}</h5>
                    <p class="mb-2">{{ module.description }}</p>
                    <small class="text-muted">
                        <strong>Version:</strong> {{ module.version }} | 
                        <strong>Module ID:</strong> {{ module.id }}
                        {% if module.dependencies is not empty %}
                            | <strong>Dependencies:</strong> {{ module.dependencies|join(', ') }}
                        {% endif %}
                    </small>
                </div>

                <!-- Configuration Form -->
                {% if form_fields is empty %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        This module has no configurable options.
                    </div>

                    <div class="mt-3">
                        <a href="{{ path('admin_modules') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Modules
                        </a>
                    </div>
                {% else %}
                    <form method="POST">
                        {% for field_name, field_config in form_fields %}
                            <div class="mb-3">
                                <label for="{{ field_name }}" class="form-label">
                                    {{ field_config.label }}
                                    {% if field_config.required|default(false) %}
                                        <span class="text-danger">*</span>
                                    {% endif %}
                                </label>

                                {% if field_config.type == 'text' %}
                                    <input type="text" 
                                           class="form-control" 
                                           id="{{ field_name }}" 
                                           name="{{ field_name }}" 
                                           value="{{ current_config[field_name]|default(field_config.default|default('')) }}"
                                           {% if field_config.required|default(false) %}required{% endif %}>

                                {% elseif field_config.type == 'textarea' %}
                                    <textarea class="form-control" 
                                              id="{{ field_name }}" 
                                              name="{{ field_name }}" 
                                              rows="{{ field_config.rows|default(3) }}"
                                              {% if field_config.required|default(false) %}required{% endif %}>{{ current_config[field_name]|default(field_config.default|default('')) }}</textarea>

                                {% elseif field_config.type == 'checkbox' %}
                                    <div class="form-check">
                                        <input type="checkbox" 
                                               class="form-check-input" 
                                               id="{{ field_name }}" 
                                               name="{{ field_name }}" 
                                               value="1"
                                               {% if current_config[field_name]|default(field_config.default|default(false)) %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ field_name }}">
                                            Enable this option
                                        </label>
                                    </div>

                                {% elseif field_config.type == 'select' %}
                                    <select class="form-select" 
                                            id="{{ field_name }}" 
                                            name="{{ field_name }}"
                                            {% if field_config.required|default(false) %}required{% endif %}>
                                        {% for option_value, option_label in field_config.options|default({}) %}
                                            <option value="{{ option_value }}" 
                                                    {% if current_config[field_name]|default(field_config.default|default('')) == option_value %}selected{% endif %}>
                                                {{ option_label }}
                                            </option>
                                        {% endfor %}
                                    </select>

                                {% elseif field_config.type == 'number' %}
                                    <input type="number" 
                                           class="form-control" 
                                           id="{{ field_name }}" 
                                           name="{{ field_name }}" 
                                           value="{{ current_config[field_name]|default(field_config.default|default('')) }}"
                                           {% if field_config.min is defined %}min="{{ field_config.min }}"{% endif %}
                                           {% if field_config.max is defined %}max="{{ field_config.max }}"{% endif %}
                                           {% if field_config.step is defined %}step="{{ field_config.step }}"{% endif %}
                                           {% if field_config.required|default(false) %}required{% endif %}>

                                {% elseif field_config.type == 'email' %}
                                    <input type="email" 
                                           class="form-control" 
                                           id="{{ field_name }}" 
                                           name="{{ field_name }}" 
                                           value="{{ current_config[field_name]|default(field_config.default|default('')) }}"
                                           {% if field_config.required|default(false) %}required{% endif %}>

                                {% elseif field_config.type == 'url' %}
                                    <input type="url" 
                                           class="form-control" 
                                           id="{{ field_name }}" 
                                           name="{{ field_name }}" 
                                           value="{{ current_config[field_name]|default(field_config.default|default('')) }}"
                                           {% if field_config.required|default(false) %}required{% endif %}>

                                {% else %}
                                    <!-- Fallback for unknown field types -->
                                    <input type="text" 
                                           class="form-control" 
                                           id="{{ field_name }}" 
                                           name="{{ field_name }}" 
                                           value="{{ current_config[field_name]|default(field_config.default|default('')) }}">
                                {% endif %}

                                {% if field_config.description is defined %}
                                    <div class="form-text">{{ field_config.description }}</div>
                                {% endif %}
                            </div>
                        {% endfor %}

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Configuration
                            </button>
                            <a href="{{ path('admin_modules') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                {% endif %}

            </div>
        </div>
    </div>
</div>

<style>
.form-label {
    font-weight: 600;
    color: #495057;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.alert h5 {
    margin-bottom: 0.5rem;
}

.alert p {
    margin-bottom: 0.5rem;
}

.text-danger {
    color: #dc3545 !important;
}

.form-check {
    padding-left: 1.5em;
}

.form-check-input {
    margin-left: -1.5em;
}

.btn {
    margin-right: 0.5rem;
}
</style>
