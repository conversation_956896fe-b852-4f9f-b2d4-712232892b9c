
<div class="row mb-4" data-controller="block-admin"
     data-block-admin-add-url-value="{{ path('admin_blocks_add') }}"
     data-block-admin-update-url-value="{{ path('admin_blocks_update', {placementId: '__ID__'}) }}"
     data-block-admin-remove-url-value="{{ path('admin_blocks_remove', {placementId: '__ID__'}) }}">
    <div class="col-12">
        <div class="card">
            <div class="card__header">
                <h3 class="card-title">Block Management</h3>
                <p class="text-muted">Drag and drop blocks between regions to organize your layout</p>
            </div>
            <div class="card-body">

                <!-- Available Blocks -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5>Available Blocks</h5>
                        <div class="available-blocks d-flex flex-wrap gap-2" id="available-blocks">
                            {% for block in available_blocks %}
                                <div class="block-item card"
                                     data-block-admin-target="availableBlock"
                                     data-block-id="{{ block.id }}"
                                     draggable="true">
                                    <div class="card-body p-2">
                                        <h6 class="card-title mb-1">{{ block.label }}</h6>
                                        <small class="text-muted">{{ block.description }}</small>
                                        <div class="badge bg-secondary">{{ block.category }}</div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Regions -->
                <div class="row">
                    {% for region_name, region_config in regions %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="region-container">
                                <h5>{{ region_config.label }}</h5>
                                <p class="text-muted small">{{ region_config.description }}</p>

                                <div class="region-dropzone border rounded p-3 min-height-200 sortable-region"
                                     data-block-admin-target="regionDropzone"
                                     data-region="{{ region_name }}"
                                     data-action="drop->block-admin#dropBlock dragover->block-admin#allowDrop">

                                    {% if blocks_by_region[region_name] is defined %}
                                        {% for placement in blocks_by_region[region_name] %}
                                            {% set block = available_blocks[placement.blockId] %}
                                            {% if block %}
                                                <div class="placed-block card mb-2"
                                                     data-block-admin-target="placedBlock"
                                                     data-placement-id="{{ placement.id }}"
                                                     data-block-id="{{ placement.blockId }}"
                                                     draggable="true">
                                                    <div class="card-body p-2">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <h6 class="card-title mb-1">{{ block.label }}</h6>
                                                                <small class="text-muted">Weight: {{ placement.weight }}</small>
                                                            </div>
                                                            <div class="btn-group btn-group-sm">
                                                                <a href="{{ path('admin_blocks_configure', {placementId: placement.id}) }}"
                                                                   class="btn btn-outline-primary btn-sm">
                                                                    <i class="fas fa-cog"></i>
                                                                </a>
                                                                <button class="btn btn-outline-danger btn-sm"
                                                                        data-action="click->block-admin#removeBlock"
                                                                        data-block-admin-placement-id-param="{{ placement.id }}">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        {% if not placement.enabled %}
                                                            <div class="badge bg-warning">Disabled</div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}

                                    <div class="drop-placeholder text-center text-muted p-3">
                                        <i class="fas fa-plus-circle"></i>
                                        <br>Drop blocks here
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.min-height-200 {
    min-height: 200px;
}

.block-item, .placed-block {
    cursor: move;
    transition: all 0.2s;
}

.block-item:hover, .placed-block:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.region-dropzone {
    background-color: #f8f9fa;
}

.region-dropzone.drag-over {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

.drop-placeholder {
    opacity: 0.5;
}

.region-dropzone:not(:empty) .drop-placeholder {
    display: none;
}

.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.placed-block {
    transition: transform 0.2s ease;
}

.placed-block:not(.dragging):hover {
    transform: translateY(-2px);
}

.sortable-region .placed-block {
    margin-bottom: 8px;
}

.sortable-region .placed-block:last-child {
    margin-bottom: 0;
}
</style>
